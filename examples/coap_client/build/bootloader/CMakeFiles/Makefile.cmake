# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/esp-idf-anviz/.git/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bootloader/subproject/components/micro-ecc/micro-ecc/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/controller/lib_esp32/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/controller/lib_esp32c3_family/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/controller/lib_esp32c6/esp32c6-bt-lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/host/nimble/nimble/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/cmock/CMock/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/esp_coex/lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/esp_phy/lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/heap/tlsf/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/ieee802154/lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/json/cJSON/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/lwip/lwip/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/mbedtls/mbedtls/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/mqtt/esp-mqtt/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/openthread/lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/openthread/openthread/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/protobuf-c/protobuf-c/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/spiffs/spiffs/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/unity/unity/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/refs/heads/dev"
  "/home/<USER>/esp-idf-anviz/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/bootloader/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/bootloader/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/bootloader/subproject/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/bootloader/subproject/components/micro-ecc/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/bootloader/subproject/components/micro-ecc/micro-ecc/.git"
  "/home/<USER>/esp-idf-anviz/components/bootloader/subproject/main/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/bootloader_support/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/bt/controller/lib_esp32/.git"
  "/home/<USER>/esp-idf-anviz/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/.git"
  "/home/<USER>/esp-idf-anviz/components/bt/controller/lib_esp32c3_family/.git"
  "/home/<USER>/esp-idf-anviz/components/bt/controller/lib_esp32c6/esp32c6-bt-lib/.git"
  "/home/<USER>/esp-idf-anviz/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/.git"
  "/home/<USER>/esp-idf-anviz/components/bt/host/nimble/nimble/.git"
  "/home/<USER>/esp-idf-anviz/components/cmock/CMock/.git"
  "/home/<USER>/esp-idf-anviz/components/efuse/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/efuse/esp32c6/sources.cmake"
  "/home/<USER>/esp-idf-anviz/components/esp_app_format/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_coex/lib/.git"
  "/home/<USER>/esp-idf-anviz/components/esp_common/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_common/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/esp_hw_support/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_hw_support/port/esp32c6/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_phy/lib/.git"
  "/home/<USER>/esp-idf-anviz/components/esp_rom/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_system/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esptool_py/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esptool_py/espefuse.cmake"
  "/home/<USER>/esp-idf-anviz/components/esptool_py/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/freertos/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/hal/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/heap/tlsf/.git"
  "/home/<USER>/esp-idf-anviz/components/ieee802154/lib/.git"
  "/home/<USER>/esp-idf-anviz/components/json/cJSON/.git"
  "/home/<USER>/esp-idf-anviz/components/log/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/lwip/lwip/.git"
  "/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/.git"
  "/home/<USER>/esp-idf-anviz/components/mqtt/esp-mqtt/.git"
  "/home/<USER>/esp-idf-anviz/components/newlib/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/openthread/lib/.git"
  "/home/<USER>/esp-idf-anviz/components/openthread/openthread/.git"
  "/home/<USER>/esp-idf-anviz/components/partition_table/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/partition_table/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/protobuf-c/protobuf-c/.git"
  "/home/<USER>/esp-idf-anviz/components/riscv/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/riscv/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/soc/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/spi_flash/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/spiffs/spiffs/.git"
  "/home/<USER>/esp-idf-anviz/components/unity/unity/.git"
  "/home/<USER>/esp-idf-anviz/tools/cmake/build.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/component.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/depgraph.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/dfu.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/git_submodules.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/idf.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/kconfig.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/ldgen.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/project.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/project_description.json.in"
  "/home/<USER>/esp-idf-anviz/tools/cmake/targets.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/third_party/GetGitRevisionDescription.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/third_party/GetGitRevisionDescription.cmake.in"
  "/home/<USER>/esp-idf-anviz/tools/cmake/tool_version_check.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/toolchain-esp32c6.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/uf2.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/utilities.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/version.cmake"
  "/home/<USER>/esp-idf-anviz/tools/kconfig_new/confgen.py"
  "/home/<USER>/esp-idf-anviz/tools/kconfig_new/config.env.in"
  "CMakeFiles/3.22.1/CMakeASMCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeSystem.cmake"
  "CMakeFiles/git-data/grabRef.cmake"
  "config/sdkconfig.cmake"
  "config/sdkconfig.h"
  "/home/<USER>/test/coap/examples/coap_client/sdkconfig"
  "/usr/share/cmake-3.22/Modules/CMakeASMInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCXXCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/CheckIncludeFile.cmake"
  "/usr/share/cmake-3.22/Modules/CheckIncludeFileCXX.cmake"
  "/usr/share/cmake-3.22/Modules/CheckTypeSize.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-ASM.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.22/Modules/ExternalProject.cmake"
  "/usr/share/cmake-3.22/Modules/FindGit.cmake"
  "/usr/share/cmake-3.22/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.22/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.22/Modules/Internal/CheckCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Generic.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/git-data/HEAD"
  "CMakeFiles/git-data/grabRef.cmake"
  "CMakeFiles/git-data/head-ref"
  "CMakeFiles/git-data/HEAD"
  "CMakeFiles/git-data/grabRef.cmake"
  "CMakeFiles/git-data/head-ref"
  "config.env"
  "project_description.json"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/riscv/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/newlib/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/soc/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/micro-ecc/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/hal/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/spi_flash/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_app_format/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/bootloader_support/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/efuse/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_system/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_hw_support/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_hw_support/port/esp32c6/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_common/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_rom/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/log/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esptool_py/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/partition_table/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/bootloader/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/freertos/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/main/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/menuconfig.dir/DependInfo.cmake"
  "CMakeFiles/confserver.dir/DependInfo.cmake"
  "CMakeFiles/save-defconfig.dir/DependInfo.cmake"
  "CMakeFiles/gen_project_binary.dir/DependInfo.cmake"
  "CMakeFiles/app.dir/DependInfo.cmake"
  "CMakeFiles/erase_flash.dir/DependInfo.cmake"
  "CMakeFiles/monitor.dir/DependInfo.cmake"
  "CMakeFiles/_project_elf_src.dir/DependInfo.cmake"
  "CMakeFiles/bootloader.elf.dir/DependInfo.cmake"
  "CMakeFiles/size.dir/DependInfo.cmake"
  "CMakeFiles/size-files.dir/DependInfo.cmake"
  "CMakeFiles/size-components.dir/DependInfo.cmake"
  "CMakeFiles/uf2-app.dir/DependInfo.cmake"
  "CMakeFiles/uf2.dir/DependInfo.cmake"
  "esp-idf/soc/CMakeFiles/__idf_soc.dir/DependInfo.cmake"
  "esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/DependInfo.cmake"
  "esp-idf/hal/CMakeFiles/__idf_hal.dir/DependInfo.cmake"
  "esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/DependInfo.cmake"
  "esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/DependInfo.cmake"
  "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse-common-table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse_common_table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/show-efuse-table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/show_efuse_table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse_test_table.dir/DependInfo.cmake"
  "esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/DependInfo.cmake"
  "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/DependInfo.cmake"
  "esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/DependInfo.cmake"
  "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/DependInfo.cmake"
  "esp-idf/log/CMakeFiles/__idf_log.dir/DependInfo.cmake"
  "esp-idf/esptool_py/CMakeFiles/bootloader_check_size.dir/DependInfo.cmake"
  "esp-idf/main/CMakeFiles/__idf_main.dir/DependInfo.cmake"
  )
