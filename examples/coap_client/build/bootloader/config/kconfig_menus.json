[{"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_SUPPORTED", "name": "SOC_ADC_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_DEDICATED_GPIO_SUPPORTED", "name": "SOC_DEDICATED_GPIO_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_UART_SUPPORTED", "name": "SOC_UART_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GDMA_SUPPORTED", "name": "SOC_GDMA_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPTIMER_SUPPORTED", "name": "SOC_GPTIMER_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PCNT_SUPPORTED", "name": "SOC_PCNT_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_SUPPORTED", "name": "SOC_MCPWM_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TWAI_SUPPORTED", "name": "SOC_TWAI_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ETM_SUPPORTED", "name": "SOC_ETM_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PARLIO_SUPPORTED", "name": "SOC_PARLIO_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BT_SUPPORTED", "name": "SOC_BT_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_IEEE802154_SUPPORTED", "name": "SOC_IEEE802154_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ASYNC_MEMCPY_SUPPORTED", "name": "SOC_ASYNC_MEMCPY_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_USB_SERIAL_JTAG_SUPPORTED", "name": "SOC_USB_SERIAL_JTAG_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TEMP_SENSOR_SUPPORTED", "name": "SOC_TEMP_SENSOR_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_WIFI_SUPPORTED", "name": "SOC_WIFI_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SUPPORTS_SECURE_DL_MODE", "name": "SOC_SUPPORTS_SECURE_DL_MODE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ULP_SUPPORTED", "name": "SOC_ULP_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LP_CORE_SUPPORTED", "name": "SOC_LP_CORE_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EFUSE_KEY_PURPOSE_FIELD", "name": "SOC_EFUSE_KEY_PURPOSE_FIELD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RTC_FAST_MEM_SUPPORTED", "name": "SOC_RTC_FAST_MEM_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RTC_MEM_SUPPORTED", "name": "SOC_RTC_MEM_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2S_SUPPORTED", "name": "SOC_I2S_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_SUPPORTED", "name": "SOC_RMT_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SDM_SUPPORTED", "name": "SOC_SDM_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPSPI_SUPPORTED", "name": "SOC_GPSPI_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LEDC_SUPPORTED", "name": "SOC_LEDC_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2C_SUPPORTED", "name": "SOC_I2C_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SYSTIMER_SUPPORTED", "name": "SOC_SYSTIMER_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SUPPORT_COEXISTENCE", "name": "SOC_SUPPORT_COEXISTENCE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_AES_SUPPORTED", "name": "SOC_AES_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MPI_SUPPORTED", "name": "SOC_MPI_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SHA_SUPPORTED", "name": "SOC_SHA_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_HMAC_SUPPORTED", "name": "SOC_HMAC_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_DIG_SIGN_SUPPORTED", "name": "SOC_DIG_SIGN_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ECC_SUPPORTED", "name": "SOC_ECC_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_FLASH_ENC_SUPPORTED", "name": "SOC_FLASH_ENC_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SECURE_BOOT_SUPPORTED", "name": "SOC_SECURE_BOOT_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SDIO_SLAVE_SUPPORTED", "name": "SOC_SDIO_SLAVE_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BOD_SUPPORTED", "name": "SOC_BOD_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_APM_SUPPORTED", "name": "SOC_APM_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PMU_SUPPORTED", "name": "SOC_PMU_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PAU_SUPPORTED", "name": "SOC_PAU_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LP_TIMER_SUPPORTED", "name": "SOC_LP_TIMER_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LP_AON_SUPPORTED", "name": "SOC_LP_AON_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LP_I2C_SUPPORTED", "name": "SOC_LP_I2C_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_XTAL_SUPPORT_40M", "name": "SOC_XTAL_SUPPORT_40M", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_AES_SUPPORT_DMA", "name": "SOC_AES_SUPPORT_DMA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_AES_GDMA", "name": "SOC_AES_GDMA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_AES_SUPPORT_AES_128", "name": "SOC_AES_SUPPORT_AES_128", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_AES_SUPPORT_AES_256", "name": "SOC_AES_SUPPORT_AES_256", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_DIG_CTRL_SUPPORTED", "name": "SOC_ADC_DIG_CTRL_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_DIG_IIR_FILTER_SUPPORTED", "name": "SOC_ADC_DIG_IIR_FILTER_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_MONITOR_SUPPORTED", "name": "SOC_ADC_MONITOR_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_DMA_SUPPORTED", "name": "SOC_ADC_DMA_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_PERIPH_NUM", "name": "SOC_ADC_PERIPH_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_MAX_CHANNEL_NUM", "name": "SOC_ADC_MAX_CHANNEL_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_ATTEN_NUM", "name": "SOC_ADC_ATTEN_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_DIGI_CONTROLLER_NUM", "name": "SOC_ADC_DIGI_CONTROLLER_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_PATT_LEN_MAX", "name": "SOC_ADC_PATT_LEN_MAX", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_DIGI_MAX_BITWIDTH", "name": "SOC_ADC_DIGI_MAX_BITWIDTH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_DIGI_MIN_BITWIDTH", "name": "SOC_ADC_DIGI_MIN_BITWIDTH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_DIGI_IIR_FILTER_NUM", "name": "SOC_ADC_DIGI_IIR_FILTER_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_DIGI_MONITOR_NUM", "name": "SOC_ADC_DIGI_MONITOR_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_DIGI_RESULT_BYTES", "name": "SOC_ADC_DIGI_RESULT_BYTES", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_DIGI_DATA_BYTES_PER_CONV", "name": "SOC_ADC_DIGI_DATA_BYTES_PER_CONV", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_SAMPLE_FREQ_THRES_HIGH", "name": "SOC_ADC_SAMPLE_FREQ_THRES_HIGH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_SAMPLE_FREQ_THRES_LOW", "name": "SOC_ADC_SAMPLE_FREQ_THRES_LOW", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_RTC_MIN_BITWIDTH", "name": "SOC_ADC_RTC_MIN_BITWIDTH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_RTC_MAX_BITWIDTH", "name": "SOC_ADC_RTC_MAX_BITWIDTH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_CALIBRATION_V1_SUPPORTED", "name": "SOC_ADC_CALIBRATION_V1_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_SELF_HW_CALI_SUPPORTED", "name": "SOC_ADC_SELF_HW_CALI_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_CALIB_CHAN_COMPENS_SUPPORTED", "name": "SOC_ADC_CALIB_CHAN_COMPENS_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ADC_TEMPERATURE_SHARE_INTR", "name": "SOC_ADC_TEMPERATURE_SHARE_INTR", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_APB_BACKUP_DMA", "name": "SOC_APB_BACKUP_DMA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BROWNOUT_RESET_SUPPORTED", "name": "SOC_BROWNOUT_RESET_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SHARED_IDCACHE_SUPPORTED", "name": "SOC_SHARED_IDCACHE_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CACHE_FREEZE_SUPPORTED", "name": "SOC_CACHE_FREEZE_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CPU_CORES_NUM", "name": "SOC_CPU_CORES_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CPU_INTR_NUM", "name": "SOC_CPU_INTR_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CPU_HAS_FLEXIBLE_INTC", "name": "SOC_CPU_HAS_FLEXIBLE_INTC", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_INT_PLIC_SUPPORTED", "name": "SOC_INT_PLIC_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CPU_BREAKPOINTS_NUM", "name": "SOC_CPU_BREAKPOINTS_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CPU_WATCHPOINTS_NUM", "name": "SOC_CPU_WATCHPOINTS_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CPU_WATCHPOINT_SIZE", "name": "SOC_CPU_WATCHPOINT_SIZE", "range": null, "title": null, "type": "hex"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CPU_HAS_PMA", "name": "SOC_CPU_HAS_PMA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CPU_IDRAM_SPLIT_USING_PMP", "name": "SOC_CPU_IDRAM_SPLIT_USING_PMP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_DS_SIGNATURE_MAX_BIT_LEN", "name": "SOC_DS_SIGNATURE_MAX_BIT_LEN", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_DS_KEY_PARAM_MD_IV_LENGTH", "name": "SOC_DS_KEY_PARAM_MD_IV_LENGTH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_DS_KEY_CHECK_MAX_WAIT_US", "name": "SOC_DS_KEY_CHECK_MAX_WAIT_US", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GDMA_GROUPS", "name": "SOC_GDMA_GROUPS", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GDMA_PAIRS_PER_GROUP", "name": "SOC_GDMA_PAIRS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GDMA_SUPPORT_ETM", "name": "SOC_GDMA_SUPPORT_ETM", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ETM_GROUPS", "name": "SOC_ETM_GROUPS", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ETM_CHANNELS_PER_GROUP", "name": "SOC_ETM_CHANNELS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_PORT", "name": "SOC_GPIO_PORT", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_PIN_COUNT", "name": "SOC_GPIO_PIN_COUNT", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER", "name": "SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_FLEX_GLITCH_FILTER_NUM", "name": "SOC_GPIO_FLEX_GLITCH_FILTER_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_SUPPORT_ETM", "name": "SOC_GPIO_SUPPORT_ETM", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_ETM_EVENTS_PER_GROUP", "name": "SOC_GPIO_ETM_EVENTS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_ETM_TASKS_PER_GROUP", "name": "SOC_GPIO_ETM_TASKS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_SUPPORT_RTC_INDEPENDENT", "name": "SOC_GPIO_SUPPORT_RTC_INDEPENDENT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP", "name": "SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK", "name": "SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK", "name": "SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK", "range": null, "title": null, "type": "hex"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GPIO_SUPPORT_FORCE_HOLD", "name": "SOC_GPIO_SUPPORT_FORCE_HOLD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_GP<PERSON>_SUPPORT_HOLD_SINGLE_IO_IN_DSLP", "name": "SOC_GP<PERSON>_SUPPORT_HOLD_SINGLE_IO_IN_DSLP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RTCIO_PIN_COUNT", "name": "SOC_RTCIO_PIN_COUNT", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RTCIO_INPUT_OUTPUT_SUPPORTED", "name": "SOC_RTCIO_INPUT_OUTPUT_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RTCIO_HOLD_SUPPORTED", "name": "SOC_RTCIO_HOLD_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RTCIO_WAKE_SUPPORTED", "name": "SOC_RTCIO_WAKE_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_DEDIC_GPIO_OUT_CHANNELS_NUM", "name": "SOC_DEDIC_GPIO_OUT_CHANNELS_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_DEDIC_GPIO_IN_CHANNELS_NUM", "name": "SOC_DEDIC_GPIO_IN_CHANNELS_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_DEDIC_PERIPH_ALWAYS_ENABLE", "name": "SOC_DEDIC_PERIPH_ALWAYS_ENABLE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2C_NUM", "name": "SOC_I2C_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2C_FIFO_LEN", "name": "SOC_I2C_FIFO_LEN", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2C_CMD_REG_NUM", "name": "SOC_I2C_CMD_REG_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2C_SUPPORT_SLAVE", "name": "SOC_I2C_SUPPORT_SLAVE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2C_SUPPORT_HW_CLR_BUS", "name": "SOC_I2C_SUPPORT_HW_CLR_BUS", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2C_SUPPORT_XTAL", "name": "SOC_I2C_SUPPORT_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2C_SUPPORT_RTC", "name": "SOC_I2C_SUPPORT_RTC", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LP_I2C_NUM", "name": "SOC_LP_I2C_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LP_I2C_FIFO_LEN", "name": "SOC_LP_I2C_FIFO_LEN", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2S_NUM", "name": "SOC_I2S_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2S_HW_VERSION_2", "name": "SOC_I2S_HW_VERSION_2", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2S_SUPPORTS_XTAL", "name": "SOC_I2S_SUPPORTS_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2S_SUPPORTS_PLL_F160M", "name": "SOC_I2S_SUPPORTS_PLL_F160M", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2S_SUPPORTS_PCM", "name": "SOC_I2S_SUPPORTS_PCM", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2S_SUPPORTS_PDM", "name": "SOC_I2S_SUPPORTS_PDM", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2S_SUPPORTS_PDM_TX", "name": "SOC_I2S_SUPPORTS_PDM_TX", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2S_PDM_MAX_TX_LINES", "name": "SOC_I2S_PDM_MAX_TX_LINES", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_I2S_SUPPORTS_TDM", "name": "SOC_I2S_SUPPORTS_TDM", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LEDC_SUPPORT_PLL_DIV_CLOCK", "name": "SOC_LEDC_SUPPORT_PLL_DIV_CLOCK", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LEDC_SUPPORT_XTAL_CLOCK", "name": "SOC_LEDC_SUPPORT_XTAL_CLOCK", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LEDC_CHANNEL_NUM", "name": "SOC_LEDC_CHANNEL_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LEDC_TIMER_BIT_WIDTH", "name": "SOC_LEDC_TIMER_BIT_WIDTH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LEDC_SUPPORT_FADE_STOP", "name": "SOC_LEDC_SUPPORT_FADE_STOP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LEDC_GAMMA_CURVE_FADE_SUPPORTED", "name": "SOC_LEDC_GAMMA_CURVE_FADE_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LEDC_GAMMA_CURVE_FADE_RANGE_MAX", "name": "SOC_LEDC_GAMMA_CURVE_FADE_RANGE_MAX", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LEDC_FADE_PARAMS_BIT_WIDTH", "name": "SOC_LEDC_FADE_PARAMS_BIT_WIDTH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MMU_PAGE_SIZE_CONFIGURABLE", "name": "SOC_MMU_PAGE_SIZE_CONFIGURABLE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MMU_PERIPH_NUM", "name": "SOC_MMU_PERIPH_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MMU_LINEAR_ADDRESS_REGION_NUM", "name": "SOC_MMU_LINEAR_ADDRESS_REGION_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MMU_DI_VADDR_SHARED", "name": "SOC_MMU_DI_VADDR_SHARED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MPU_CONFIGURABLE_REGIONS_SUPPORTED", "name": "SOC_MPU_CONFIGURABLE_REGIONS_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MPU_MIN_REGION_SIZE", "name": "SOC_MPU_MIN_REGION_SIZE", "range": null, "title": null, "type": "hex"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MPU_REGIONS_MAX_NUM", "name": "SOC_MPU_REGIONS_MAX_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MPU_REGION_RO_SUPPORTED", "name": "SOC_MPU_REGION_RO_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MPU_REGION_WO_SUPPORTED", "name": "SOC_MPU_REGION_WO_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PCNT_GROUPS", "name": "SOC_PCNT_GROUPS", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PCNT_UNITS_PER_GROUP", "name": "SOC_PCNT_UNITS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PCNT_CHANNELS_PER_UNIT", "name": "SOC_PCNT_CHANNELS_PER_UNIT", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PCNT_THRES_POINT_PER_UNIT", "name": "SOC_PCNT_THRES_POINT_PER_UNIT", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PCNT_SUPPORT_RUNTIME_THRES_UPDATE", "name": "SOC_PCNT_SUPPORT_RUNTIME_THRES_UPDATE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_GROUPS", "name": "SOC_RMT_GROUPS", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_TX_CANDIDATES_PER_GROUP", "name": "SOC_RMT_TX_CANDIDATES_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_RX_CANDIDATES_PER_GROUP", "name": "SOC_RMT_RX_CANDIDATES_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_CHANNELS_PER_GROUP", "name": "SOC_RMT_CHANNELS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_MEM_WORDS_PER_CHANNEL", "name": "SOC_RMT_MEM_WORDS_PER_CHANNEL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_SUPPORT_RX_PINGPONG", "name": "SOC_RMT_SUPPORT_RX_PINGPONG", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_SUPPORT_RX_DEMODULATION", "name": "SOC_RMT_SUPPORT_RX_DEMODULATION", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_SUPPORT_TX_ASYNC_STOP", "name": "SOC_RMT_SUPPORT_TX_ASYNC_STOP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_SUPPORT_TX_LOOP_COUNT", "name": "SOC_RMT_SUPPORT_TX_LOOP_COUNT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP", "name": "SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_SUPPORT_TX_SYNCHRO", "name": "SOC_RMT_SUPPORT_TX_SYNCHRO", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY", "name": "SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_SUPPORT_XTAL", "name": "SOC_RMT_SUPPORT_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RMT_SUPPORT_RC_FAST", "name": "SOC_RMT_SUPPORT_RC_FAST", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_GROUPS", "name": "SOC_MCPWM_GROUPS", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_TIMERS_PER_GROUP", "name": "SOC_MCPWM_TIMERS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_OPERATORS_PER_GROUP", "name": "SOC_MCPWM_OPERATORS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_COMPARATORS_PER_OPERATOR", "name": "SOC_MCPWM_COMPARATORS_PER_OPERATOR", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_GENERATORS_PER_OPERATOR", "name": "SOC_MCPWM_GENERATORS_PER_OPERATOR", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_TRIGGERS_PER_OPERATOR", "name": "SOC_MCPWM_TRIGGERS_PER_OPERATOR", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_GPIO_FAULTS_PER_GROUP", "name": "SOC_MCPWM_GPIO_FAULTS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP", "name": "SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER", "name": "SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP", "name": "SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_SWSYNC_CAN_PROPAGATE", "name": "SOC_MCPWM_SWSYNC_CAN_PROPAGATE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_SUPPORT_ETM", "name": "SOC_MCPWM_SUPPORT_ETM", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MCPWM_CAPTURE_CLK_FROM_GROUP", "name": "SOC_MCPWM_CAPTURE_CLK_FROM_GROUP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PARLIO_GROUPS", "name": "SOC_PARLIO_GROUPS", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PARLIO_TX_UNITS_PER_GROUP", "name": "SOC_PARLIO_TX_UNITS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PARLIO_RX_UNITS_PER_GROUP", "name": "SOC_PARLIO_RX_UNITS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PARLIO_TX_UNIT_MAX_DATA_WIDTH", "name": "SOC_PARLIO_TX_UNIT_MAX_DATA_WIDTH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PARLIO_RX_UNIT_MAX_DATA_WIDTH", "name": "SOC_PARLIO_RX_UNIT_MAX_DATA_WIDTH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PARLIO_TX_RX_SHARE_INTERRUPT", "name": "SOC_PARLIO_TX_RX_SHARE_INTERRUPT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_RSA_MAX_BIT_LEN", "name": "SOC_RSA_MAX_BIT_LEN", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SHA_DMA_MAX_BUFFER_SIZE", "name": "SOC_SHA_DMA_MAX_BUFFER_SIZE", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SHA_SUPPORT_DMA", "name": "SOC_SHA_SUPPORT_DMA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SHA_SUPPORT_RESUME", "name": "SOC_SHA_SUPPORT_RESUME", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SHA_GDMA", "name": "SOC_SHA_GDMA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SHA_SUPPORT_SHA1", "name": "SOC_SHA_SUPPORT_SHA1", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SHA_SUPPORT_SHA224", "name": "SOC_SHA_SUPPORT_SHA224", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SHA_SUPPORT_SHA256", "name": "SOC_SHA_SUPPORT_SHA256", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SDM_GROUPS", "name": "SOC_SDM_GROUPS", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SDM_CHANNELS_PER_GROUP", "name": "SOC_SDM_CHANNELS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SDM_CLK_SUPPORT_PLL_F80M", "name": "SOC_SDM_CLK_SUPPORT_PLL_F80M", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SDM_CLK_SUPPORT_XTAL", "name": "SOC_SDM_CLK_SUPPORT_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_PERIPH_NUM", "name": "SOC_SPI_PERIPH_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_MAX_CS_NUM", "name": "SOC_SPI_MAX_CS_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_MAXIMUM_BUFFER_SIZE", "name": "SOC_SPI_MAXIMUM_BUFFER_SIZE", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_SUPPORT_DDRCLK", "name": "SOC_SPI_SUPPORT_DDRCLK", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_SLAVE_SUPPORT_SEG_TRANS", "name": "SOC_SPI_SLAVE_SUPPORT_SEG_TRANS", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_SUPPORT_CD_SIG", "name": "SOC_SPI_SUPPORT_CD_SIG", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_SUPPORT_CONTINUOUS_TRANS", "name": "SOC_SPI_SUPPORT_CONTINUOUS_TRANS", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_SUPPORT_SLAVE_HD_VER2", "name": "SOC_SPI_SUPPORT_SLAVE_HD_VER2", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_SUPPORT_CLK_XTAL", "name": "SOC_SPI_SUPPORT_CLK_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_SUPPORT_CLK_PLL_F80M", "name": "SOC_SPI_SUPPORT_CLK_PLL_F80M", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_SUPPORT_CLK_RC_FAST", "name": "SOC_SPI_SUPPORT_CLK_RC_FAST", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MEMSPI_IS_INDEPENDENT", "name": "SOC_MEMSPI_IS_INDEPENDENT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_MAX_PRE_DIVIDER", "name": "SOC_SPI_MAX_PRE_DIVIDER", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE", "name": "SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND", "name": "SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_MEM_SUPPORT_AUTO_RESUME", "name": "SOC_SPI_MEM_SUPPORT_AUTO_RESUME", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_MEM_SUPPORT_IDLE_INTR", "name": "SOC_SPI_MEM_SUPPORT_IDLE_INTR", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_MEM_SUPPORT_SW_SUSPEND", "name": "SOC_SPI_MEM_SUPPORT_SW_SUSPEND", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_MEM_SUPPORT_CHECK_SUS", "name": "SOC_SPI_MEM_SUPPORT_CHECK_SUS", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SPI_MEM_SUPPORT_WRAP", "name": "SOC_SPI_MEM_SUPPORT_WRAP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED", "name": "SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED", "name": "SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED", "name": "SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SYSTIMER_COUNTER_NUM", "name": "SOC_SYSTIMER_COUNTER_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SYSTIMER_ALARM_NUM", "name": "SOC_SYSTIMER_ALARM_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SYSTIMER_BIT_WIDTH_LO", "name": "SOC_SYSTIMER_BIT_WIDTH_LO", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SYSTIMER_BIT_WIDTH_HI", "name": "SOC_SYSTIMER_BIT_WIDTH_HI", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SYSTIMER_FIXED_DIVIDER", "name": "SOC_SYSTIMER_FIXED_DIVIDER", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SYSTIMER_SUPPORT_RC_FAST", "name": "SOC_SYSTIMER_SUPPORT_RC_FAST", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SYSTIMER_INT_LEVEL", "name": "SOC_SYSTIMER_INT_LEVEL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SYSTIMER_ALARM_MISS_COMPENSATE", "name": "SOC_SYSTIMER_ALARM_MISS_COMPENSATE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SYSTIMER_SUPPORT_ETM", "name": "SOC_SYSTIMER_SUPPORT_ETM", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LP_TIMER_BIT_WIDTH_LO", "name": "SOC_LP_TIMER_BIT_WIDTH_LO", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_LP_TIMER_BIT_WIDTH_HI", "name": "SOC_LP_TIMER_BIT_WIDTH_HI", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TIMER_GROUPS", "name": "SOC_TIMER_GROUPS", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TIMER_GROUP_TIMERS_PER_GROUP", "name": "SOC_TIMER_GROUP_TIMERS_PER_GROUP", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TIMER_GROUP_COUNTER_BIT_WIDTH", "name": "SOC_TIMER_GROUP_COUNTER_BIT_WIDTH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TIMER_GROUP_SUPPORT_XTAL", "name": "SOC_TIMER_GROUP_SUPPORT_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TIMER_GROUP_SUPPORT_RC_FAST", "name": "SOC_TIMER_GROUP_SUPPORT_RC_FAST", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TIMER_GROUP_TOTAL_TIMERS", "name": "SOC_TIMER_GROUP_TOTAL_TIMERS", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TIMER_SUPPORT_ETM", "name": "SOC_TIMER_SUPPORT_ETM", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MWDT_SUPPORT_XTAL", "name": "SOC_MWDT_SUPPORT_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TWAI_CONTROLLER_NUM", "name": "SOC_TWAI_CONTROLLER_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TWAI_CLK_SUPPORT_XTAL", "name": "SOC_TWAI_CLK_SUPPORT_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TWAI_BRP_MIN", "name": "SOC_TWAI_BRP_MIN", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TWAI_BRP_MAX", "name": "SOC_TWAI_BRP_MAX", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TWAI_SUPPORTS_RX_STATUS", "name": "SOC_TWAI_SUPPORTS_RX_STATUS", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EFUSE_DIS_DOWNLOAD_ICACHE", "name": "SOC_EFUSE_DIS_DOWNLOAD_ICACHE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EFUSE_DIS_PAD_JTAG", "name": "SOC_EFUSE_DIS_PAD_JTAG", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EFUSE_DIS_USB_JTAG", "name": "SOC_EFUSE_DIS_USB_JTAG", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EFUSE_DIS_DIRECT_BOOT", "name": "SOC_EFUSE_DIS_DIRECT_BOOT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EFUSE_SOFT_DIS_JTAG", "name": "SOC_EFUSE_SOFT_DIS_JTAG", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EFUSE_DIS_ICACHE", "name": "SOC_EFUSE_DIS_ICACHE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EFUSE_BLOCK9_KEY_PURPOSE_QUIRK", "name": "SOC_EFUSE_BLOCK9_KEY_PURPOSE_QUIRK", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SECURE_BOOT_V2_RSA", "name": "SOC_SECURE_BOOT_V2_RSA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SECURE_BOOT_V2_ECC", "name": "SOC_SECURE_BOOT_V2_ECC", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS", "name": "SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS", "name": "SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY", "name": "SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX", "name": "SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_FLASH_ENCRYPTION_XTS_AES", "name": "SOC_FLASH_ENCRYPTION_XTS_AES", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_FLASH_ENCRYPTION_XTS_AES_128", "name": "SOC_FLASH_ENCRYPTION_XTS_AES_128", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CRYPTO_DPA_PROTECTION_SUPPORTED", "name": "SOC_CRYPTO_DPA_PROTECTION_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_UART_NUM", "name": "SOC_UART_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_UART_FIFO_LEN", "name": "SOC_UART_FIFO_LEN", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_UART_BITRATE_MAX", "name": "SOC_UART_BITRATE_MAX", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_UART_SUPPORT_PLL_F80M_CLK", "name": "SOC_UART_SUPPORT_PLL_F80M_CLK", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_UART_SUPPORT_RTC_CLK", "name": "SOC_UART_SUPPORT_RTC_CLK", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_UART_SUPPORT_XTAL_CLK", "name": "SOC_UART_SUPPORT_XTAL_CLK", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_UART_SUPPORT_WAKEUP_INT", "name": "SOC_UART_SUPPORT_WAKEUP_INT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_UART_SUPPORT_FSM_TX_WAIT_SEND", "name": "SOC_UART_SUPPORT_FSM_TX_WAIT_SEND", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_COEX_HW_PTI", "name": "SOC_COEX_HW_PTI", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EXTERNAL_COEX_ADVANCE", "name": "SOC_EXTERNAL_COEX_ADVANCE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_EXTERNAL_COEX_LEADER_TX_LINE", "name": "SOC_EXTERNAL_COEX_LEADER_TX_LINE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PHY_DIG_REGS_MEM_SIZE", "name": "SOC_PHY_DIG_REGS_MEM_SIZE", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH", "name": "SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_WIFI_WAKEUP", "name": "SOC_PM_SUPPORT_WIFI_WAKEUP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_BEACON_WAKEUP", "name": "SOC_PM_SUPPORT_BEACON_WAKEUP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_BT_WAKEUP", "name": "SOC_PM_SUPPORT_BT_WAKEUP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_EXT1_WAKEUP", "name": "SOC_PM_SUPPORT_EXT1_WAKEUP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_CPU_PD", "name": "SOC_PM_SUPPORT_CPU_PD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_MODEM_PD", "name": "SOC_PM_SUPPORT_MODEM_PD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_XTAL32K_PD", "name": "SOC_PM_SUPPORT_XTAL32K_PD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_RC32K_PD", "name": "SOC_PM_SUPPORT_RC32K_PD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_RC_FAST_PD", "name": "SOC_PM_SUPPORT_RC_FAST_PD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_VDDSDIO_PD", "name": "SOC_PM_SUPPORT_VDDSDIO_PD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_TOP_PD", "name": "SOC_PM_SUPPORT_TOP_PD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_HP_AON_PD", "name": "SOC_PM_SUPPORT_HP_AON_PD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_MAC_BB_PD", "name": "SOC_PM_SUPPORT_MAC_BB_PD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_RTC_PERIPH_PD", "name": "SOC_PM_SUPPORT_RTC_PERIPH_PD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_PMU_MODEM_STATE", "name": "SOC_PM_SUPPORT_PMU_MODEM_STATE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY", "name": "SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_CPU_RETENTION_BY_SW", "name": "SOC_PM_CPU_RETENTION_BY_SW", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_MODEM_RETENTION_BY_REGDMA", "name": "SOC_PM_MODEM_RETENTION_BY_REGDMA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_RETENTION_HAS_CLOCK_BUG", "name": "SOC_PM_RETENTION_HAS_CLOCK_BUG", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PM_PAU_LINK_NUM", "name": "SOC_PM_PAU_LINK_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CLK_RC_FAST_SUPPORT_CALIBRATION", "name": "SOC_CLK_RC_FAST_SUPPORT_CALIBRATION", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_MODEM_CLOCK_IS_INDEPENDENT", "name": "SOC_MODEM_CLOCK_IS_INDEPENDENT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CLK_XTAL32K_SUPPORTED", "name": "SOC_CLK_XTAL32K_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CLK_OSC_SLOW_SUPPORTED", "name": "SOC_CLK_OSC_SLOW_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_CLK_RC32K_SUPPORTED", "name": "SOC_CLK_RC32K_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TEMPERATURE_SENSOR_SUPPORT_FAST_RC", "name": "SOC_TEMPERATURE_SENSOR_SUPPORT_FAST_RC", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TEMPERATURE_SENSOR_SUPPORT_XTAL", "name": "SOC_TEMPERATURE_SENSOR_SUPPORT_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_TEMPERATURE_SENSOR_INTR_SUPPORT", "name": "SOC_TEMPERATURE_SENSOR_INTR_SUPPORT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_WIFI_HW_TSF", "name": "SOC_WIFI_HW_TSF", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_WIFI_FTM_SUPPORT", "name": "SOC_WIFI_FTM_SUPPORT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_WIFI_GCMP_SUPPORT", "name": "SOC_WIFI_GCMP_SUPPORT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_WIFI_WAPI_SUPPORT", "name": "SOC_WIFI_WAPI_SUPPORT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_WIFI_CSI_SUPPORT", "name": "SOC_WIFI_CSI_SUPPORT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_WIFI_MESH_SUPPORT", "name": "SOC_WIFI_MESH_SUPPORT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_WIFI_HE_SUPPORT", "name": "SOC_WIFI_HE_SUPPORT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BLE_SUPPORTED", "name": "SOC_BLE_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BLE_MESH_SUPPORTED", "name": "SOC_BLE_MESH_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_ESP_NIMBLE_CONTROLLER", "name": "SOC_ESP_NIMBLE_CONTROLLER", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BLE_50_SUPPORTED", "name": "SOC_BLE_50_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BLE_DEVICE_PRIVACY_SUPPORTED", "name": "SOC_BLE_DEVICE_PRIVACY_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BLE_POWER_CONTROL_SUPPORTED", "name": "SOC_BLE_POWER_CONTROL_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BLE_PERIODIC_ADV_ENH_SUPPORTED", "name": "SOC_BLE_PERIODIC_ADV_ENH_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BLUFI_SUPPORTED", "name": "SOC_BLUFI_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BLE_MULTI_CONN_OPTIMIZATION", "name": "SOC_BLE_MULTI_CONN_OPTIMIZATION", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_BLE_USE_WIFI_PWR_CLK_WORKAROUND", "name": "SOC_BLE_USE_WIFI_PWR_CLK_WORKAROUND", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SOC_PHY_COMBO_MODULE", "name": "SOC_PHY_COMBO_MODULE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_CMAKE", "name": "IDF_CMAKE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_ENV_FPGA", "name": "IDF_ENV_FPGA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_CI_BUILD", "name": "IDF_CI_BUILD", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ARCH_RISCV", "name": "IDF_TARGET_ARCH_RISCV", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ARCH_XTENSA", "name": "IDF_TARGET_ARCH_XTENSA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ARCH", "name": "IDF_TARGET_ARCH", "range": null, "title": null, "type": "string"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET", "name": "IDF_TARGET", "range": null, "title": null, "type": "string"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_LINUX", "name": "IDF_TARGET_LINUX", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32", "name": "IDF_TARGET_ESP32", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32S2", "name": "IDF_TARGET_ESP32S2", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32S3", "name": "IDF_TARGET_ESP32S3", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32C3", "name": "IDF_TARGET_ESP32C3", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32C2", "name": "IDF_TARGET_ESP32C2", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32C6", "name": "IDF_TARGET_ESP32C6", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32H2", "name": "IDF_TARGET_ESP32H2", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_LINUX", "name": "IDF_TARGET_LINUX", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_FIRMWARE_CHIP_ID", "name": "IDF_FIRMWARE_CHIP_ID", "range": null, "title": null, "type": "hex"}, {"children": [{"children": [{"children": [], "depends_on": "!IDF_TARGET_LINUX && <choice APP_BUILD_TYPE>", "help": null, "id": "APP_BUILD_TYPE_APP_2NDBOOT", "name": "APP_BUILD_TYPE_APP_2NDBOOT", "range": null, "title": "Default (binary application + 2nd stage bootloader)", "type": "bool"}, {"children": [], "depends_on": "<choice APP_BUILD_TYPE>", "help": null, "id": "APP_BUILD_TYPE_RAM", "name": "APP_BUILD_TYPE_RAM", "range": null, "title": "Build app runs entirely in RAM (EXPERIMENTAL)", "type": "bool"}], "depends_on": null, "help": "Select the way the application is built.\n\nBy default, the application is built as a binary file in a format compatible with\nthe ESP-IDF bootloader. In addition to this application, 2nd stage bootloader is\nalso built. Application and bootloader binaries can be written into flash and\nloaded/executed from there.\n\nAnother option, useful for only very small and limited applications, is to only link\nthe .elf file of the application, such that it can be loaded directly into RAM over\nJTAG or UART. Note that since IRAM and DRAM sizes are very limited, it is not possible\nto build any complex application this way. However for some kinds of testing and debugging,\nthis option may provide faster iterations, since the application does not need to be\nwritten into flash.\n\nNote: when APP_BUILD_TYPE_RAM is selected and loaded with JTAG, ESP-IDF does not contain\nall the startup code required to initialize the CPUs and ROM memory (data/bss).\nTherefore it is necessary to execute a bit of ROM code prior to executing the application.\nA gdbinit file may look as follows (for ESP32):\n\n    # Connect to a running instance of OpenOCD\n    target remote :3333\n    # Reset and halt the target\n    mon reset halt\n    # Run to a specific point in ROM code,\n    #  where most of initialization is complete.\n    thb *0x40007d54\n    c\n    # Load the application into RAM\n    load\n    # Run till app_main\n    tb app_main\n    c\n\nExecute this gdbinit file as follows:\n\n    xtensa-esp32-elf-gdb build/app-name.elf -x gdbinit\n\nExample gdbinit files for other targets can be found in tools/test_apps/system/gdb_loadable_elf/\n\nWhen loading the BIN with UART, the ROM will jump to ram and run the app after finishing the ROM\nstartup code, so there's no additional startup initialization required. You can use the\n`load_ram` in esptool.py to load the generated .bin file into ram and execute.\n\nExample:\n    esptool.py --chip {chip} -p {port} -b {baud} --no-stub load_ram {app.bin}\n\nRecommended sdkconfig.defaults for building loadable ELF files is as follows.\nCONFIG_APP_BUILD_TYPE_RAM is required, other options help reduce application\nmemory footprint.\n\n    CONFIG_APP_BUILD_TYPE_RAM=y\n    CONFIG_VFS_SUPPORT_TERMIOS=\n    CONFIG_NEWLIB_NANO_FORMAT=y\n    CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT=y\n    CONFIG_ESP_DEBUG_STUBS_ENABLE=\n    CONFIG_ESP_ERR_TO_NAME_LOOKUP=", "id": "build-type-application-build-type", "name": "APP_BUILD_TYPE", "title": "Application build type", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "APP_BUILD_GENERATE_BINARIES", "name": "APP_BUILD_GENERATE_BINARIES", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "APP_BUILD_BOOTLOADER", "name": "APP_BUILD_BOOTLOADER", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "APP_BUILD_TYPE_RAM", "help": "If this option is enabled, external memory and related peripherals, such as Cache, MMU,\nFlash and PSRAM, won't be initialized. Corresponding drivers won't be introduced either.\nComponents that depend on the spi_flash component will also be unavailable, such as\napp_update, etc. When this option is enabled, about 26KB of RAM space can be saved.", "id": "APP_BUILD_TYPE_PURE_RAM_APP", "name": "APP_BUILD_TYPE_PURE_RAM_APP", "range": null, "title": "Build app without SPI_FLASH/PSRAM support (saves ram)", "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "APP_BUILD_USE_FLASH_SECTIONS", "name": "APP_BUILD_USE_FLASH_SECTIONS", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": "If enabled, all date, time, and path information would be eliminated. A .gdbinit file would be create\nautomatically. (or will be append if you have one already)", "id": "APP_REPRODUCIBLE_BUILD", "name": "APP_REPRODUCIBLE_BUILD", "range": null, "title": "Enable reproducible build", "type": "bool"}, {"children": [], "depends_on": null, "help": "If enabled, this disables the linking of binary libraries in the application build. Note\nthat after enabling this Wi-Fi/Bluetooth will not work.", "id": "APP_NO_BLOBS", "name": "APP_NO_BLOBS", "range": null, "title": "No Binary Blobs", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32", "help": "Bootloaders before ESP-IDF v2.1 did less initialisation of the\nsystem clock. This setting needs to be enabled to build an app\nwhich can be booted by these older bootloaders.\n\nIf this setting is enabled, the app can be booted by any bootloader\nfrom IDF v1.0 up to the current version.\n\nIf this setting is disabled, the app can only be booted by bootloaders\nfrom IDF v2.1 or newer.\n\nEnabling this setting adds approximately 1KB to the app's IRAM usage.", "id": "APP_COMPATIBLE_PRE_V2_1_BOOTLOADERS", "name": "APP_COMPATIBLE_PRE_V2_1_BOOTLOADERS", "range": null, "title": "App compatible with bootloaders before ESP-IDF v2.1", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32", "help": "Partition tables before ESP-IDF V3.1 do not contain an MD5 checksum\nfield, and the bootloader before ESP-IDF v3.1 cannot read a partition\ntable that contains an MD5 checksum field.\n\nEnable this option only if your app needs to boot on a bootloader and/or\npartition table that was generated from a version *before* ESP-IDF v3.1.\n\nIf this option and Flash Encryption are enabled at the same time, and any\ndata partitions in the partition table are marked Encrypted, then the\npartition encrypted flag should be manually verified in the app before accessing\nthe partition (see CVE-2021-27926).", "id": "APP_COMPATIBLE_PRE_V3_1_BOOTLOADERS", "name": "APP_COMPATIBLE_PRE_V3_1_BOOTLOADERS", "range": null, "title": "App compatible with bootloader and partition table before ESP-IDF v3.1", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32", "help": null, "id": "APP_INIT_CLK", "name": "APP_INIT_CLK", "range": null, "title": null, "type": "bool"}], "depends_on": null, "id": "build-type", "title": "Build type", "type": "menu"}, {"children": [{"children": [], "depends_on": null, "help": "Offset address that 2nd bootloader will be flashed to.\nThe value is determined by the ROM bootloader.\nIt's not configurable in ESP-IDF.", "id": "BOOTLOADER_OFFSET_IN_FLASH", "name": "BOOTLOADER_OFFSET_IN_FLASH", "range": null, "title": null, "type": "hex"}, {"children": [{"children": [], "depends_on": "<choice BOOTLOADER_COMPILER_OPTIMIZATION>", "help": null, "id": "BOOTLOADER_COMPILER_OPTIMIZATION_SIZE", "name": "BOOTLOADER_COMPILER_OPTIMIZATION_SIZE", "range": null, "title": "Size (-Os)", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_COMPILER_OPTIMIZATION>", "help": null, "id": "BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG", "name": "BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG", "range": null, "title": "Debug (-Og)", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_COMPILER_OPTIMIZATION>", "help": null, "id": "BOOTLOADER_COMPILER_OPTIMIZATION_PERF", "name": "BOOTLOADER_COMPILER_OPTIMIZATION_PERF", "range": null, "title": "Optimize for performance (-O2)", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_COMPILER_OPTIMIZATION>", "help": null, "id": "BOOTLOADER_COMPILER_OPTIMIZATION_NONE", "name": "BOOTLOADER_COMPILER_OPTIMIZATION_NONE", "range": null, "title": "Debug without optimization (-O0)", "type": "bool"}], "depends_on": null, "help": "This option sets compiler optimization level (gcc -O argument)\nfor the bootloader.\n\n- The default \"Size\" setting will add the -0s flag to CFLAGS.\n- The \"Debug\" setting will add the -Og flag to CFLAGS.\n- The \"Performance\" setting will add the -O2 flag to CFLAGS.\n- The \"None\" setting will add the -O0 flag to CFLAGS.\n\nNote that custom optimization levels may be unsupported.", "id": "bootloader-config-bootloader-optimization-level", "name": "BOOTLOADER_COMPILER_OPTIMIZATION", "title": "Bootloader optimization Level", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_NONE", "name": "BOOTLOADER_LOG_LEVEL_NONE", "range": null, "title": "No output", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_ERROR", "name": "BOOTLOADER_LOG_LEVEL_ERROR", "range": null, "title": "Error", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_WARN", "name": "BOOTLOADER_LOG_LEVEL_WARN", "range": null, "title": "Warning", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_INFO", "name": "BOOTLOADER_LOG_LEVEL_INFO", "range": null, "title": "Info", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_DEBUG", "name": "BOOTLOADER_LOG_LEVEL_DEBUG", "range": null, "title": "Debug", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_VERBOSE", "name": "BOOTLOADER_LOG_LEVEL_VERBOSE", "range": null, "title": "Verbose", "type": "bool"}], "depends_on": null, "help": "Specify how much output to see in bootloader logs.", "id": "bootloader-config-bootloader-log-verbosity", "name": "BOOTLOADER_LOG_LEVEL", "title": "Bootloader log verbosity", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "BOOTLOADER_LOG_LEVEL", "name": "BOOTLOADER_LOG_LEVEL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": "IDF_TARGET_ESP32 && (ESPTOOLPY_FLASHMODE_QIO || ESPTOOLPY_FLASHMODE_QOUT)", "help": "This setting is only used if the SPI flash pins have been overridden by setting the eFuses\nSPI_PAD_CONFIG_xxx, and the SPI flash mode is QIO or QOUT.\n\nWhen this is the case, the eFuse config only defines 3 of the 4 Quad I/O data pins. The WP pin (aka\nESP32 pin \"SD_DATA_3\" or SPI flash pin \"IO2\") is not specified in eFuse. The same pin is also used\nfor external SPIRAM if it is enabled.\n\nIf this config item is set to N (default), the correct WP pin will be automatically used for any\nEspressif chip or module with integrated flash. If a custom setting is needed, set this config item to\nY and specify the GPIO number connected to the WP.", "id": "BOOTLOADER_SPI_CUSTOM_WP_PIN", "name": "BOOTLOADER_SPI_CUSTOM_WP_PIN", "range": null, "title": "Use custom SPI Flash WP Pin when flash pins set in eFuse (read help)", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32 && (ESPTOOLPY_FLASHMODE_QIO || ESPTOOLPY_FLASHMODE_QOUT)", "help": "The option \"Use custom SPI Flash WP Pin\" must be set or this value is ignored\n\nIf burning a customized set of SPI flash pins in eFuse and using QIO or QOUT mode for flash, set this\nvalue to the GPIO number of the SPI flash WP pin.", "id": "BOOTLOADER_SPI_WP_PIN", "name": "BOOTLOADER_SPI_WP_PIN", "range": null, "title": "Custom SPI Flash WP Pin", "type": "int"}, {"children": [{"children": [], "depends_on": "!ESPTOOLPY_FLASHFREQ_80M && <choice BOOTLOADER_VDDSDIO_BOOST>", "help": null, "id": "BOOTLOADER_VDDSDIO_BOOST_1_8V", "name": "BOOTLOADER_VDDSDIO_BOOST_1_8V", "range": null, "title": "1.8V", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_VDDSDIO_BOOST>", "help": null, "id": "BOOTLOADER_VDDSDIO_BOOST_1_9V", "name": "BOOTLOADER_VDDSDIO_BOOST_1_9V", "range": null, "title": "1.9V", "type": "bool"}], "depends_on": "SOC_CONFIGURABLE_VDDSDIO_SUPPORTED", "help": "If this option is enabled, and VDDSDIO LDO is set to 1.8V (using eFuse\nor MTDI bootstrapping pin), bootloader will change LDO settings to\noutput 1.9V instead. This helps prevent flash chip from browning out\nduring flash programming operations.\n\nThis option has no effect if VDDSDIO is set to 3.3V, or if the internal\nVDDSDIO regulator is disabled via eFuse.", "id": "bootloader-config-vddsdio-ldo-voltage", "name": "BOOTLOADER_VDDSDIO_BOOST", "title": "VDDSDIO LDO voltage", "type": "choice"}, {"children": [{"children": [], "depends_on": "BOOTLOADER_FACTORY_RESET", "help": "The selected GPIO will be configured as an input with internal pull-up enabled (note that on some SoCs.\nnot all pins have an internal pull-up, consult the hardware datasheet for details.) To trigger a factory\nreset, this GPIO must be held high or low (as configured) on startup.", "id": "BOOTLOADER_NUM_PIN_FACTORY_RESET", "name": "BOOTLOADER_NUM_PIN_FACTORY_RESET", "range": null, "title": "Number of the GPIO input for factory reset", "type": "int"}, {"children": [{"children": [], "depends_on": "<choice BOOTLOADER_FACTORY_RESET_PIN_LEVEL>", "help": null, "id": "BOOTLOADER_FACTORY_RESET_PIN_LOW", "name": "BOOTLOADER_FACTORY_RESET_PIN_LOW", "range": null, "title": "Reset on GPIO low", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_FACTORY_RESET_PIN_LEVEL>", "help": null, "id": "BOOTLOADER_FACTORY_RESET_PIN_HIGH", "name": "BOOTLOADER_FACTORY_RESET_PIN_HIGH", "range": null, "title": "Reset on GPIO high", "type": "bool"}], "depends_on": "BOOTLOADER_FACTORY_RESET", "help": "Pin level for factory reset, can be triggered on low or high.", "id": "bootloader-config-gpio-triggers-factory-reset-factory-reset-gpio-level", "name": "BOOTLOADER_FACTORY_RESET_PIN_LEVEL", "title": "Factory reset GPIO level", "type": "choice"}, {"children": [], "depends_on": "BOOTLOADER_FACTORY_RESET", "help": "The device will boot from \"factory\" partition (or OTA slot 0 if no factory partition is present) after a\nfactory reset.", "id": "BOOTLOADER_OTA_DATA_ERASE", "name": "BOOTLOADER_OTA_DATA_ERASE", "range": null, "title": "Clear OTA data on factory reset (select factory partition)", "type": "bool"}, {"children": [], "depends_on": "BOOTLOADER_FACTORY_RESET", "help": "Allows customers to select which data partitions will be erased while factory reset.\n\nSpecify the names of partitions as a comma-delimited with optional spaces for readability. (Like this:\n\"nvs, phy_init, ...\")\nMake sure that the name specified in the partition table and here are the same.\nPartitions of type \"app\" cannot be specified here.", "id": "BOOTLOADER_DATA_FACTORY_RESET", "name": "BOOTLOADER_DATA_FACTORY_RESET", "range": null, "title": "Comma-separated names of partitions to clear on factory reset", "type": "string"}], "depends_on": null, "help": "Allows to reset the device to factory settings:\n- clear one or more data partitions;\n- boot from \"factory\" partition.\nThe factory reset will occur if there is a GPIO input held at the configured level while\ndevice starts up. See settings below.", "id": "BOOTLOADER_FACTORY_RESET", "name": "BOOTLOADER_FACTORY_RESET", "range": null, "title": "GPIO triggers factory reset", "type": "bool"}, {"children": [{"children": [], "depends_on": "BOOTLOADER_APP_TEST", "help": "The selected GPIO will be configured as an input with internal pull-up enabled.\nTo trigger a test app, this GPIO must be pulled low on reset.\nAfter the GPIO input is deactivated and the device reboots, the old application will boot.\n(factory or OTA[x]).\nNote that GPIO34-39 do not have an internal pullup and an external one must be provided.", "id": "BOOTLOADER_NUM_PIN_APP_TEST", "name": "BOOTLOADER_NUM_PIN_APP_TEST", "range": null, "title": "Number of the GPIO input to boot TEST partition", "type": "int"}, {"children": [{"children": [], "depends_on": "<choice BOOTLOADER_APP_TEST_PIN_LEVEL>", "help": null, "id": "BOOTLOADER_APP_TEST_PIN_LOW", "name": "BOOTLOADER_APP_TEST_PIN_LOW", "range": null, "title": "Enter test app on GPIO low", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_APP_TEST_PIN_LEVEL>", "help": null, "id": "BOOTLOADER_APP_TEST_PIN_HIGH", "name": "BOOTLOADER_APP_TEST_PIN_HIGH", "range": null, "title": "Enter test app on GPIO high", "type": "bool"}], "depends_on": "BOOTLOADER_APP_TEST", "help": "Pin level for app test, can be triggered on low or high.", "id": "bootloader-config-gpio-triggers-boot-from-test-app-partition-app-test-gpio-level", "name": "BOOTLOADER_APP_TEST_PIN_LEVEL", "title": "App test GPIO level", "type": "choice"}], "depends_on": "!BOOTLOADER_APP_ANTI_ROLLBACK", "help": "Allows to run the test app from \"TEST\" partition.\nA boot from \"test\" partition will occur if there is a GPIO input pulled low while device starts up.\nSee settings below.", "id": "BOOTLOADER_APP_TEST", "name": "BOOTLOADER_APP_TEST", "range": null, "title": "GPIO triggers boot from test app partition", "type": "bool"}, {"children": [], "depends_on": "BOOTLOADER_FACTORY_RESET || BOOTLOADER_APP_TEST", "help": "The GPIO must be held low continuously for this period of time after reset\nbefore a factory reset or test partition boot (as applicable) is performed.", "id": "BOOTLOADER_HOLD_TIME_GPIO", "name": "BOOTLOADER_HOLD_TIME_GPIO", "range": null, "title": "Hold time of GPIO for reset/test mode (seconds)", "type": "int"}, {"children": [], "depends_on": null, "help": "Protects the unmapped memory regions of the entire address space from unintended accesses.\nThis will ensure that an exception will be triggered whenever the CPU performs a memory\noperation on unmapped regions of the address space.", "id": "BOOTLOADER_REGION_PROTECTION_ENABLE", "name": "BOOTLOADER_REGION_PROTECTION_ENABLE", "range": null, "title": "Enable protection for unmapped memory regions", "type": "bool"}, {"children": [{"children": [], "depends_on": "BOOTLOADER_WDT_ENABLE", "help": "If this option is set, the ESP-IDF app must explicitly reset, feed, or disable the rtc_wdt in\nthe app's own code.\nIf this option is not set (default), then rtc_wdt will be disabled by ESP-IDF before calling\nthe app_main() function.\n\nUse function rtc_wdt_feed() for resetting counter of rtc_wdt.\nUse function rtc_wdt_disable() for disabling rtc_wdt.", "id": "BOOTLOADER_WDT_DISABLE_IN_USER_CODE", "name": "BOOTLOADER_WDT_DISABLE_IN_USER_CODE", "range": null, "title": "Allows RTC watchdog disable in user code", "type": "bool"}, {"children": [], "depends_on": "BOOTLOADER_WDT_ENABLE", "help": "Verify that this parameter is correct and more then the execution time.\nPay attention to options such as reset to factory, trigger test partition and encryption on boot\n- these options can increase the execution time.\nNote: RTC_WDT will reset while encryption operations will be performed.", "id": "BOOTLOADER_WDT_TIME_MS", "name": "BOOTLOADER_WDT_TIME_MS", "range": [0, 120000], "title": "Timeout for RTC watchdog (ms)", "type": "int"}], "depends_on": null, "help": "Tracks the execution time of startup code.\nIf the execution time is exceeded, the RTC_WDT will restart system.\nIt is also useful to prevent a lock up in start code caused by an unstable power source.\nNOTE: Tracks the execution time starts from the bootloader code - re-set timeout, while selecting the\nsource for slow_clk - and ends calling app_main.\nRe-set timeout is needed due to WDT uses a SLOW_CLK clock source. After changing a frequency slow_clk a\ntime of WDT needs to re-set for new frequency.\nslow_clk depends on RTC_CLK_SRC (INTERNAL_RC or EXTERNAL_CRYSTAL).", "id": "BOOTLOADER_WDT_ENABLE", "name": "BOOTLOADER_WDT_ENABLE", "range": null, "title": "Use RTC watchdog in start code", "type": "bool"}, {"children": [{"children": [{"children": [], "depends_on": "BOOTLOADER_APP_ANTI_ROLLBACK", "help": "The secure version is the sequence number stored in the header of each firmware.\nThe security version is set in the bootloader, version is recorded in the eFuse field\nas the number of set ones. The allocated number of bits in the efuse field\nfor storing the security version is limited (see BOOTLOADER_APP_SEC_VER_SIZE_EFUSE_FIELD option).\n\nBootloader: When bootloader selects an app to boot, an app is selected that has\na security version greater or equal that recorded in eFuse field.\nThe app is booted with a higher (or equal) secure version.\n\nThe security version is worth increasing if in previous versions there is\na significant vulnerability and their use is not acceptable.\n\nYour partition table should has a scheme with ota_0 + ota_1 (without factory).", "id": "BOOTLOADER_APP_SECURE_VERSION", "name": "BOOTLOADER_APP_SECURE_VERSION", "range": null, "title": "eFuse secure version of app", "type": "int"}, {"children": [], "depends_on": "BOOTLOADER_APP_ANTI_ROLLBACK", "help": "The size of the efuse secure version field.\nIts length is limited to 32 bits for ESP32 and 16 bits for ESP32-S2.\nThis determines how many times the security version can be increased.", "id": "BOOTLOADER_APP_SEC_VER_SIZE_EFUSE_FIELD", "name": "BOOTLOADER_APP_SEC_VER_SIZE_EFUSE_FIELD", "range": null, "title": "Size of the efuse secure version field", "type": "int"}, {"children": [], "depends_on": "BOOTLOADER_APP_ANTI_ROLLBACK", "help": "This option allows to emulate read/write operations with all eFuses and efuse secure version.\nIt allows to test anti-rollback implemention without permanent write eFuse bits.\nThere should be an entry in partition table with following details: `emul_efuse, data, efuse, , 0x2000`.\n\nThis option enables: EFUSE_VIRTUAL and EFUSE_VIRTUAL_KEEP_IN_FLASH.", "id": "BOOTLOADER_EFUSE_SECURE_VERSION_EMULATE", "name": "BOOTLOADER_EFUSE_SECURE_VERSION_EMULATE", "range": null, "title": "Emulate operations with efuse secure version(only test)", "type": "bool"}], "depends_on": "BOOTLOADER_APP_ROLLBACK_ENABLE", "help": "This option prevents rollback to previous firmware/application image with lower security version.", "id": "BOOTLOADER_APP_ANTI_ROLLBACK", "name": "BOOTLOADER_APP_ANTI_ROLLBACK", "range": null, "title": "Enable app anti-rollback support", "type": "bool"}], "depends_on": null, "help": "After updating the app, the bootloader runs a new app with the \"ESP_OTA_IMG_PENDING_VERIFY\" state set.\nThis state prevents the re-run of this app. After the first boot of the new app in the user code, the\nfunction should be called to confirm the operability of the app or vice versa about its non-operability.\nIf the app is working, then it is marked as valid. Otherwise, it is marked as not valid and rolls back to\nthe previous working app. A reboot is performed, and the app is booted before the software update.\nNote: If during the first boot a new app the power goes out or the WDT works, then roll back will happen.\nRollback is possible only between the apps with the same security versions.", "id": "BOOTLOADER_APP_ROLLBACK_ENABLE", "name": "BOOTLOADER_APP_ROLLBACK_ENABLE", "range": null, "title": "Enable app rollback support", "type": "bool"}, {"children": [], "depends_on": "SOC_RTC_FAST_MEM_SUPPORTED && ((SECURE_BOOT && SECURE_BOOT_INSECURE) || !SECURE_BOOT)", "help": "This option disables the normal validation of an image coming out of\ndeep sleep (checksums, SHA256, and signature). This is a trade-off\nbetween wakeup performance from deep sleep, and image integrity checks.\n\nOnly enable this if you know what you are doing. It should not be used\nin conjunction with using deep_sleep() entry and changing the active OTA\npartition as this would skip the validation upon first load of the new\nOTA partition.\n\nIt is possible to enable this option with Secure Boot if \"allow insecure\noptions\" is enabled, however it's strongly recommended to NOT enable it as\nit may allow a Secure Boot bypass.", "id": "BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP", "name": "BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP", "range": null, "title": "Skip image validation when exiting deep sleep", "type": "bool"}, {"children": [], "depends_on": "!SECURE_SIGNED_ON_BOOT", "help": "Some applications need to boot very quickly from power on. By default, the entire app binary\nis read from flash and verified which takes up a significant portion of the boot time.\n\nEnabling this option will skip validation of the app when the SoC boots from power on.\nNote that in this case it's not possible for the bootloader to detect if an app image is\ncorrupted in the flash, therefore it's not possible to safely fall back to a different app\npartition. Flash corruption of this kind is unlikely but can happen if there is a serious\nfirmware bug or physical damage.\n\nFollowing other reset types, the bootloader will still validate the app image. This increases\nthe chances that flash corruption resulting in a crash can be detected following soft reset, and\nthe bootloader will fall back to a valid app image. To increase the chances of successfully recovering\nfrom a flash corruption event, keep the option BOOTLOADER_WDT_ENABLE enabled and consider also enabling\nBOOTLOADER_WDT_DISABLE_IN_USER_CODE - then manually disable the RTC Watchdog once the app is running.\nIn addition, enable both the Task and Interrupt watchdog timers with reset options set.", "id": "BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON", "name": "BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON", "range": null, "title": "Skip image validation from power on reset (READ HELP FIRST)", "type": "bool"}, {"children": [], "depends_on": "!SECURE_SIGNED_ON_BOOT", "help": "Selecting this option prevents the bootloader from ever validating the app image before\nbooting it. Any flash corruption of the selected app partition will make the entire SoC\nunbootable.\n\nAlthough flash corruption is a very rare case, it is not recommended to select this option.\nConsider selecting \"Skip image validation from power on reset\" instead. However, if boot time\nis the only important factor then it can be enabled.", "id": "BOOTLOADER_SKIP_VALIDATE_ALWAYS", "name": "BOOTLOADER_SKIP_VALIDATE_ALWAYS", "range": null, "title": "Skip image validation always (READ HELP FIRST)", "type": "bool"}, {"children": [], "depends_on": "SOC_RTC_FAST_MEM_SUPPORTED", "help": "Reserve RTC FAST memory for Skip image validation. This option in bytes.\nThis option reserves an area in the RTC FAST memory (access only PRO_CPU).\nUsed to save the addresses of the selected application.\nWhen a wakeup occurs (from Deep sleep), the bootloader retrieves it and\nloads the application without validation.", "id": "BOOTLOADER_RESERVE_RTC_SIZE", "name": "BOOTLOADER_RESERVE_RTC_SIZE", "range": null, "title": null, "type": "hex"}, {"children": [{"children": [], "depends_on": "BOOTLOADER_CUSTOM_RESERVE_RTC", "help": "This option reserves in RTC FAST memory the area for custom purposes.\nIf you want to create your own bootloader and save more information\nin this area of memory, you can increase it. It must be a multiple of 4 bytes.\nThis area (rtc_retain_mem_t) is reserved and has access from the bootloader and an application.", "id": "BOOTLOADER_CUSTOM_RESERVE_RTC_SIZE", "name": "BOOTLOADER_CUSTOM_RESERVE_RTC_SIZE", "range": null, "title": "Size in bytes for custom purposes", "type": "hex"}], "depends_on": "SOC_RTC_FAST_MEM_SUPPORTED", "help": "This option allows the customer to place data in the RTC FAST memory,\nthis area remains valid when rebooted, except for power loss.\nThis memory is located at a fixed address and is available\nfor both the bootloader and the application.\n(The application and bootoloader must be compiled with the same option).\nThe RTC FAST memory has access only through PRO_CPU.", "id": "BOOTLOADER_CUSTOM_RESERVE_RTC", "name": "BOOTLOADER_CUSTOM_RESERVE_RTC", "range": null, "title": "Reserve RTC FAST memory for custom purposes", "type": "bool"}, {"children": [], "depends_on": "SOC_RTC_FAST_MEM_SUPPORTED", "help": "This option reserves an area in RTC FAST memory for the following features:\n- \"Skip image validation when exiting deep sleep\"\n- \"Reserve RTC FAST memory for custom purposes\"\n- \"GPIO triggers factory reset\"", "id": "BOOTLOADER_RESERVE_RTC_MEM", "name": "BOOTLOADER_RESERVE_RTC_MEM", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": "Perform the startup flow recommended by XMC. Please consult XMC for the details of this flow.\nXMC chips will be forbidden to be used, when this option is disabled.\n\nDON'T DISABLE THIS UNLESS YOU KNOW WHAT YOU ARE DOING.", "id": "BOOTLOADER_FLASH_XMC_SUPPORT", "name": "BOOTLOADER_FLASH_XMC_SUPPORT", "range": null, "title": "Enable the support for flash chips of XMC (READ HELP FIRST)", "type": "bool"}], "depends_on": null, "id": "bootloader-config", "title": "Bootloader config", "type": "menu"}, {"children": [{"children": [], "depends_on": "SECURE_BOOT || SECURE_SIGNED_ON_BOOT_NO_SECURE_BOOT", "help": null, "id": "SECURE_SIGNED_ON_BOOT", "name": "SECURE_SIGNED_ON_BOOT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT || SECURE_SIGNED_ON_UPDATE_NO_SECURE_BOOT", "help": null, "id": "SECURE_SIGNED_ON_UPDATE", "name": "SECURE_SIGNED_ON_UPDATE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "SECURE_SIGNED_ON_BOOT || SECURE_SIGNED_ON_UPDATE", "help": null, "id": "SECURE_SIGNED_APPS", "name": "SECURE_SIGNED_APPS", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "(IDF_TARGET_ESP32 && ESP32_REV_MIN_FULL >= 300) || SOC_SECURE_BOOT_V2_RSA", "help": null, "id": "SECURE_BOOT_V2_RSA_SUPPORTED", "name": "SECURE_BOOT_V2_RSA_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "SOC_SECURE_BOOT_V2_ECC", "help": null, "id": "SECURE_BOOT_V2_ECC_SUPPORTED", "name": "SECURE_BOOT_V2_ECC_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "SOC_SECURE_BOOT_V1", "help": null, "id": "SECURE_BOOT_V1_SUPPORTED", "name": "SECURE_BOOT_V1_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "ESP32_REV_MIN_FULL >= 300", "help": null, "id": "SECURE_BOOT_V2_PREFERRED", "name": "SECURE_BOOT_V2_PREFERRED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SECURE_BOOT_V2_ECDSA_ENABLED", "name": "SECURE_BOOT_V2_ECDSA_ENABLED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SECURE_BOOT_V2_RSA_ENABLED", "name": "SECURE_BOOT_V2_RSA_ENABLED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SECURE_BOOT_FLASH_ENC_KEYS_BURN_TOGETHER", "name": "SECURE_BOOT_FLASH_ENC_KEYS_BURN_TOGETHER", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "!SECURE_BOOT", "help": "Require apps to be signed to verify their integrity.\n\nThis option uses the same app signature scheme as hardware secure boot, but unlike hardware secure boot it\ndoes not prevent the bootloader from being physically updated. This means that the device can be secured\nagainst remote network access, but not physical access. Compared to using hardware Secure Boot this option\nis much simpler to implement.", "id": "SECURE_SIGNED_APPS_NO_SECURE_BOOT", "name": "SECURE_SIGNED_APPS_NO_SECURE_BOOT", "range": null, "title": "Require signed app images", "type": "bool"}, {"children": [{"children": [], "depends_on": "SECURE_BOOT_V1_SUPPORTED && (SECURE_SIGNED_APPS_NO_SECURE_BOOT || SECURE_BOOT_V1_ENABLED) && <choice SECURE_SIGNED_APPS_SCHEME>", "help": "Embeds the ECDSA public key in the bootloader and signs the application with an ECDSA key.\nRefer to the documentation before enabling.", "id": "SECURE_SIGNED_APPS_ECDSA_SCHEME", "name": "SECURE_SIGNED_APPS_ECDSA_SCHEME", "range": null, "title": "ECDSA", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_V2_RSA_SUPPORTED && (SECURE_SIGNED_APPS_NO_SECURE_BOOT || SECURE_BOOT_V2_ENABLED) && <choice SECURE_SIGNED_APPS_SCHEME>", "help": "Appends the RSA-3072 based Signature block to the application.\nRefer to <Secure Boot Version 2 documentation link> before enabling.", "id": "SECURE_SIGNED_APPS_RSA_SCHEME", "name": "SECURE_SIGNED_APPS_RSA_SCHEME", "range": null, "title": "RSA", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_V2_ECC_SUPPORTED && (SECURE_SIGNED_APPS_NO_SECURE_BOOT || SECURE_BOOT_V2_ENABLED) && <choice SECURE_SIGNED_APPS_SCHEME>", "help": "For Secure boot V2 (e.g., ESP32-C2 SoC), appends ECDSA based signature block to the application.\nRefer to documentation before enabling.", "id": "SECURE_SIGNED_APPS_ECDSA_V2_SCHEME", "name": "SECURE_SIGNED_APPS_ECDSA_V2_SCHEME", "range": null, "title": "ECDSA (V2)", "type": "bool"}], "depends_on": "SECURE_BOOT || SECURE_SIGNED_APPS_NO_SECURE_BOOT", "help": "Select the Secure App signing scheme. Depends on the Chip Revision.\nThere are two secure boot versions:\n\n1. Secure boot V1\n    - Legacy custom secure boot scheme. Supported in ESP32 SoC.\n\n2. Secure boot V2\n    - RSA based secure boot scheme.\n      Supported in ESP32-ECO3 (ESP32 Chip Revision 3 onwards), ESP32-S2, ESP32-C3, ESP32-S3 SoCs.\n\n    - ECDSA based secure boot scheme. Supported in ESP32-C2 SoC.", "id": "security-features-app-signing-scheme", "name": "SECURE_SIGNED_APPS_SCHEME", "title": "App Signing Scheme", "type": "choice"}, {"children": [{"children": [], "depends_on": "SECURE_SIGNED_APPS_ECDSA_V2_SCHEME && <choice SECURE_BOOT_ECDSA_KEY_LEN_SIZE>", "help": null, "id": "SECURE_BOOT_ECDSA_KEY_LEN_192_BITS", "name": "SECURE_BOOT_ECDSA_KEY_LEN_192_BITS", "range": null, "title": "Using ECC curve NISTP192", "type": "bool"}, {"children": [], "depends_on": "SECURE_SIGNED_APPS_ECDSA_V2_SCHEME && <choice SECURE_BOOT_ECDSA_KEY_LEN_SIZE>", "help": null, "id": "SECURE_BOOT_ECDSA_KEY_LEN_256_BITS", "name": "SECURE_BOOT_ECDSA_KEY_LEN_256_BITS", "range": null, "title": "Using ECC curve NISTP256 (Recommended)", "type": "bool"}], "depends_on": "SECURE_SIGNED_APPS_ECDSA_V2_SCHEME", "help": "Select the ECDSA key size. Two key sizes are supported\n\n- 192 bit key using NISTP192 curve\n- 256 bit key using NISTP256 curve (Recommended)\n\nThe advantage of using 256 bit key is the extra randomness which makes it difficult to be\nbruteforced compared to 192 bit key.\nAt present, both key sizes are practically implausible to bruteforce.", "id": "security-features-ecdsa-key-size", "name": "SECURE_BOOT_ECDSA_KEY_LEN_SIZE", "title": "ECDSA key size", "type": "choice"}, {"children": [], "depends_on": "SECURE_SIGNED_APPS_NO_SECURE_BOOT && SECURE_SIGNED_APPS_ECDSA_SCHEME", "help": "If this option is set, the bootloader will be compiled with code to verify that an app is signed before\nbooting it.\n\nIf hardware secure boot is enabled, this option is always enabled and cannot be disabled.\nIf hardware secure boot is not enabled, this option doesn't add significant security by itself so most\nusers will want to leave it disabled.", "id": "SECURE_SIGNED_ON_BOOT_NO_SECURE_BOOT", "name": "SECURE_SIGNED_ON_BOOT_NO_SECURE_BOOT", "range": null, "title": "Bootloader verifies app signatures", "type": "bool"}, {"children": [], "depends_on": "SECURE_SIGNED_APPS_NO_SECURE_BOOT", "help": "If this option is set, any OTA updated apps will have the signature verified before being considered valid.\n\nWhen enabled, the signature is automatically checked whenever the esp_ota_ops.h APIs are used for OTA\nupdates, or esp_image_format.h APIs are used to verify apps.\n\nIf hardware secure boot is enabled, this option is always enabled and cannot be disabled.\nIf hardware secure boot is not enabled, this option still adds significant security against network-based\nattackers by preventing spoofing of OTA updates.", "id": "SECURE_SIGNED_ON_UPDATE_NO_SECURE_BOOT", "name": "SECURE_SIGNED_ON_UPDATE_NO_SECURE_BOOT", "range": null, "title": "Verify app signature on update", "type": "bool"}, {"children": [{"children": [{"children": [], "depends_on": "SECURE_BOOT_V1_SUPPORTED && <choice SECURE_BOOT_VERSION>", "help": "Build a bootloader which enables secure boot version 1 on first boot.\nRefer to the Secure Boot section of the ESP-IDF Programmer's Guide for this version before enabling.", "id": "SECURE_BOOT_V1_ENABLED", "name": "SECURE_BOOT_V1_ENABLED", "range": null, "title": "Enable Secure Boot version 1", "type": "bool"}, {"children": [], "depends_on": "(SECURE_BOOT_V2_RSA_SUPPORTED || SECURE_BOOT_V2_ECC_SUPPORTED) && <choice SECURE_BOOT_VERSION>", "help": "Build a bootloader which enables Secure Boot version 2 on first boot.\nRefer to Secure Boot V2 section of the ESP-IDF Programmer's Guide for this version before enabling.", "id": "SECURE_BOOT_V2_ENABLED", "name": "SECURE_BOOT_V2_ENABLED", "range": null, "title": "Enable Secure Boot version 2", "type": "bool"}], "depends_on": "SECURE_BOOT", "help": "Select the Secure Boot Version. Depends on the Chip Revision.\nSecure Boot V2 is the new RSA / ECDSA based secure boot scheme.\n\n    - RSA based scheme is supported in ESP32 (Revision 3 onwards), ESP32-S2, ESP32-C3 (ECO3), ESP32-S3.\n    - ECDSA based scheme is supported in ESP32-C2 SoC.\n\nPlease note that, RSA or ECDSA secure boot is property of specific SoC based on its HW design, supported\ncrypto accelerators, die-size, cost and similar parameters. Please note that RSA scheme has requirement\nfor bigger key sizes but at the same time it is comparatively faster than ECDSA verification.\n\nSecure Boot V1 is the AES based (custom) secure boot scheme supported in ESP32 SoC.", "id": "security-features-enable-hardware-secure-boot-in-bootloader-read-docs-first--select-secure-boot-version", "name": "SECURE_BOOT_VERSION", "title": "Select secure boot version", "type": "choice"}], "depends_on": "SOC_SECURE_BOOT_SUPPORTED && !(IDF_TARGET_ESP32C3 && ESP32C3_REV_MIN_FULL < 3)", "help": "Build a bootloader which enables Secure Boot on first boot.\n\nOnce enabled, Secure Boot will not boot a modified bootloader. The bootloader will only load a partition\ntable or boot an app if the data has a verified digital signature. There are implications for reflashing\nupdated apps once secure boot is enabled.\n\nWhen enabling secure boot, JTAG and ROM BASIC Interpreter are permanently disabled by default.", "id": "SECURE_BOOT", "name": "SECURE_BOOT", "range": null, "title": "Enable hardware Secure Boot in bootloader (READ DOCS FIRST)", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice SECURE_BOOTLOADER_MODE>", "help": "On first boot, the bootloader will generate a key which is not readable externally or by software. A\ndigest is generated from the bootloader image itself. This digest will be verified on each subsequent\nboot.\n\nEnabling this option means that the bootloader cannot be changed after the first time it is booted.", "id": "SECURE_BOOTLOADER_ONE_TIME_FLASH", "name": "SECURE_BOOTLOADER_ONE_TIME_FLASH", "range": null, "title": "One-time flash", "type": "bool"}, {"children": [], "depends_on": "<choice SECURE_BOOTLOADER_MODE>", "help": "Generate a reusable secure bootloader key, derived (via SHA-256) from the secure boot signing key.\n\nThis allows the secure bootloader to be re-flashed by anyone with access to the secure boot signing\nkey.\n\nThis option is less secure than one-time flash, because a leak of the digest key from one device\nallows reflashing of any device that uses it.", "id": "SECURE_BOOTLOADER_REFLASHABLE", "name": "SECURE_BOOTLOADER_REFLASHABLE", "range": null, "title": "Reflashable", "type": "bool"}], "depends_on": "SECURE_BOOT_V1_ENABLED", "help": null, "id": "security-features-secure-bootloader-mode", "name": "SECURE_BOOTLOADER_MODE", "title": "Secure bootloader mode", "type": "choice"}, {"children": [{"children": [], "depends_on": "SECURE_BOOT_BUILD_SIGNED_BIN<PERSON>IES", "help": "Path to the key file used to sign app images.\n\nKey file is an ECDSA private key (NIST256p curve) in PEM format for Secure Boot V1.\nKey file is an RSA private key in PEM format for Secure Boot V2.\n\nPath is evaluated relative to the project directory.\n\nYou can generate a new signing key by running the following command:\nespsecure.py generate_signing_key secure_boot_signing_key.pem\n\nSee the Secure Boot section of the ESP-IDF Programmer's Guide for this version for details.", "id": "SECURE_BOOT_SIGNING_KEY", "name": "SECURE_BOOT_SIGNING_KEY", "range": null, "title": "Secure boot private signing key", "type": "string"}], "depends_on": "SECURE_SIGNED_APPS", "help": "Once secure boot or signed app requirement is enabled, app images are required to be signed.\n\nIf enabled (default), these binary files are signed as part of the build process. The file named in\n\"Secure boot private signing key\" will be used to sign the image.\n\nIf disabled, unsigned app/partition data will be built. They must be signed manually using espsecure.py.\nVersion 1 to enable ECDSA Based Secure Boot and Version 2 to enable RSA based Secure Boot.\n(for example, on a remote signing server.)", "id": "SECURE_BOOT_BUILD_SIGNED_BIN<PERSON>IES", "name": "SECURE_BOOT_BUILD_SIGNED_BIN<PERSON>IES", "range": null, "title": "Sign binaries during build", "type": "bool"}, {"children": [], "depends_on": "SECURE_SIGNED_APPS && SECURE_SIGNED_APPS_ECDSA_SCHEME && !SECURE_BOOT_BUILD_SIGNED_BINARIES", "help": "Path to a public key file used to verify signed images.\nSecure Boot V1: This ECDSA public key is compiled into the bootloader and/or\napp, to verify app images.\n\nKey file is in raw binary format, and can be extracted from a\nPEM formatted private key using the espsecure.py\nextract_public_key command.\n\nRefer to the Secure Boot section of the ESP-IDF Programmer's Guide for this version before enabling.", "id": "SECURE_BOOT_VERIFICATION_KEY", "name": "SECURE_BOOT_VERIFICATION_KEY", "range": null, "title": "Secure boot public signature verification key", "type": "string"}, {"children": [], "depends_on": "SECURE_BOOT && SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY", "help": "If this option is set, ROM bootloader will revoke the public key digest burned in efuse block\nif it fails to verify the signature of software bootloader with it.\nRevocation of keys does not happen when enabling secure boot. Once secure boot is enabled,\nkey revocation checks will be done on subsequent boot-up, while verifying the software bootloader\n\nThis feature provides a strong resistance against physical attacks on the device.\n\nNOTE: Once a digest slot is revoked, it can never be used again to verify an image\nThis can lead to permanent bricking of the device, in case all keys are revoked\nbecause of signature verification failure.", "id": "SECURE_BOOT_ENABLE_AGGRESSIVE_KEY_REVOKE", "name": "SECURE_BOOT_ENABLE_AGGRESSIVE_KEY_REVOKE", "range": null, "title": "Enable Aggressive key revoke strategy", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice SECURE_BOOTLOADER_KEY_ENCODING>", "help": null, "id": "SECURE_BOOTLOADER_KEY_ENCODING_256BIT", "name": "SECURE_BOOTLOADER_KEY_ENCODING_256BIT", "range": null, "title": "No encoding (256 bit key)", "type": "bool"}, {"children": [], "depends_on": "<choice SECURE_BOOTLOADER_KEY_ENCODING>", "help": null, "id": "SECURE_BOOTLOADER_KEY_ENCODING_192BIT", "name": "SECURE_BOOTLOADER_KEY_ENCODING_192BIT", "range": null, "title": "3/4 encoding (192 bit key)", "type": "bool"}], "depends_on": "SECURE_BOOTLOADER_REFLASHABLE", "help": "In reflashable secure bootloader mode, a hardware key is derived from the signing key (with SHA-256) and\ncan be written to eFuse with espefuse.py.\n\nNormally this is a 256-bit key, but if 3/4 Coding Scheme is used on the device then the eFuse key is\ntruncated to 192 bits.\n\nThis configuration item doesn't change any firmware code, it only changes the size of key binary which is\ngenerated at build time.", "id": "security-features-hardware-key-encoding", "name": "SECURE_BOOTLOADER_KEY_ENCODING", "title": "Hardware Key Encoding", "type": "choice"}, {"children": [], "depends_on": "SECURE_BOOT", "help": "You can disable some of the default protections offered by secure boot, in order to enable testing or a\ncustom combination of security features.\n\nOnly enable these options if you are very sure.\n\nRefer to the Secure Boot section of the ESP-IDF Programmer's Guide for this version before enabling.", "id": "SECURE_BOOT_INSECURE", "name": "SECURE_BOOT_INSECURE", "range": null, "title": "Allow potentially insecure options", "type": "bool"}, {"children": [{"children": [{"children": [], "depends_on": "SOC_FLASH_ENCRYPTION_XTS_AES_128_DERIVED && <choice SECURE_FLASH_ENCRYPTION_KEYSIZE>", "help": null, "id": "SECURE_FLASH_ENCRYPTION_AES128_DERIVED", "name": "SECURE_FLASH_ENCRYPTION_AES128_DERIVED", "range": null, "title": "AES-128 key derived from 128 bits (SHA256(128 bits))", "type": "bool"}, {"children": [], "depends_on": "SOC_FLASH_ENCRYPTION_XTS_AES_128 && !(IDF_TARGET_ESP32C2 && SECURE_BOOT) && <choice SECURE_FLASH_ENCRYPTION_KEYSIZE>", "help": null, "id": "SECURE_FLASH_ENCRYPTION_AES128", "name": "SECURE_FLASH_ENCRYPTION_AES128", "range": null, "title": "AES-128 (256-bit key)", "type": "bool"}, {"children": [], "depends_on": "SOC_FLASH_ENCRYPTION_XTS_AES_256 && <choice SECURE_FLASH_ENCRYPTION_KEYSIZE>", "help": null, "id": "SECURE_FLASH_ENCRYPTION_AES256", "name": "SECURE_FLASH_ENCRYPTION_AES256", "range": null, "title": "AES-256 (512-bit key)", "type": "bool"}], "depends_on": "SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS && SECURE_FLASH_ENC_ENABLED", "help": "Size of generated AES-XTS key.\n\n- AES-128 uses a 256-bit key (32 bytes) derived from 128 bits (16 bytes) burned in half Efuse key block.\n  Internally, it calculates SHA256(128 bits)\n- AES-128 uses a 256-bit key (32 bytes) which occupies one Efuse key block.\n- AES-256 uses a 512-bit key (64 bytes) which occupies two Efuse key blocks.\n\nThis setting is ignored if either type of key is already burned to Efuse before the first boot.\nIn this case, the pre-burned key is used and no new key is generated.", "id": "security-features-enable-flash-encryption-on-boot-read-docs-first--size-of-generated-aes-xts-key", "name": "SECURE_FLASH_ENCRYPTION_KEYSIZE", "title": "Size of generated AES-XTS key", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice SECURE_FLASH_ENCRYPTION_MODE>", "help": null, "id": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT", "name": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT", "range": null, "title": "Development (NOT SECURE)", "type": "bool"}, {"children": [], "depends_on": "(!EFUSE_VIRTUAL || IDF_CI_BUILD) && <choice SECURE_FLASH_ENCRYPTION_MODE>", "help": null, "id": "SECURE_FLASH_ENCRYPTION_MODE_RELEASE", "name": "SECURE_FLASH_ENCRYPTION_MODE_RELEASE", "range": null, "title": "Release", "type": "bool"}], "depends_on": "SECURE_FLASH_ENC_ENABLED", "help": "By default Development mode is enabled which allows ROM download mode to perform flash encryption\noperations (plaintext is sent to the device, and it encrypts it internally and writes ciphertext\nto flash.) This mode is not secure, it's possible for an attacker to write their own chosen plaintext\nto flash.\n\nRelease mode should always be selected for production or manufacturing. Once enabled it's no longer\npossible for the device in ROM Download Mode to use the flash encryption hardware.\n\nWhen EFUSE_VIRTUAL is enabled, SECURE_FLASH_ENCRYPTION_MODE_RELEASE is not available.\nFor CI tests we use IDF_CI_BUILD to bypass it (\"export IDF_CI_BUILD=1\").\nWe do not recommend bypassing it for other purposes.\n\nRefer to the Flash Encryption section of the ESP-IDF Programmer's Guide for details.", "id": "security-features-enable-flash-encryption-on-boot-read-docs-first--enable-usage-mode", "name": "SECURE_FLASH_ENCRYPTION_MODE", "title": "Enable usage mode", "type": "choice"}], "depends_on": null, "help": "If this option is set, flash contents will be encrypted by the bootloader on first boot.\n\nNote: After first boot, the system will be permanently encrypted. Re-flashing an encrypted\nsystem is complicated and not always possible.\n\nRead https://docs.espressif.com/projects/esp-idf/en/latest/security/flash-encryption.html\nbefore enabling.", "id": "SECURE_FLASH_ENC_ENABLED", "name": "SECURE_FLASH_ENC_ENABLED", "range": null, "title": "Enable flash encryption on boot (READ DOCS FIRST)", "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SECURE_FLASH_HAS_WRITE_PROTECTION_CACHE", "name": "SECURE_FLASH_HAS_WRITE_PROTECTION_CACHE", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [], "depends_on": "(SECURE_BOOT_INSECURE || SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT) && IDF_TARGET_ESP32", "help": "By default, the BASIC ROM Console starts on reset if no valid bootloader is\nread from the flash.\n\nWhen either flash encryption or secure boot are enabled, the default is to\ndisable this BASIC fallback mode permanently via eFuse.\n\nIf this option is set, this eFuse is not burned and the BASIC ROM Console may\nremain accessible.  Only set this option in testing environments.", "id": "SECURE_BOOT_ALLOW_ROM_BASIC", "name": "SECURE_BOOT_ALLOW_ROM_BASIC", "range": null, "title": "Leave ROM BASIC Interpreter available on reset", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_INSECURE || SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT", "help": "If not set (default), the bootloader will permanently disable JTAG (across entire chip) on first boot\nwhen either secure boot or flash encryption is enabled.\n\nSetting this option leaves JTAG on for debugging, which negates all protections of flash encryption\nand some of the protections of secure boot.\n\nOnly set this option in testing environments.", "id": "SECURE_BOOT_ALLOW_JTAG", "name": "SECURE_BOOT_ALLOW_JTAG", "range": null, "title": "Allow JTAG Debugging", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_INSECURE || SECURE_SIGNED_ON_UPDATE_NO_SECURE_BOOT", "help": "If not set (default), app partition size must be a multiple of 64KB. App images are padded to 64KB\nlength, and the bootloader checks any trailing bytes after the signature (before the next 64KB\nboundary) have not been written. This is because flash cache maps entire 64KB pages into the address\nspace. This prevents an attacker from appending unverified data after the app image in the flash,\ncausing it to be mapped into the address space.\n\nSetting this option allows the app partition length to be unaligned, and disables padding of the app\nimage to this length. It is generally not recommended to set this option, unless you have a legacy\npartitioning scheme which doesn't support 64KB aligned partition lengths.", "id": "SECURE_BOOT_ALLOW_SHORT_APP_PARTITION", "name": "SECURE_BOOT_ALLOW_SHORT_APP_PARTITION", "range": null, "title": "Allow app partition length not 64KB aligned", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_INSECURE && SECURE_BOOT_V2_ENABLED", "help": "If not set (default, recommended), on first boot the bootloader will burn the WR_DIS_RD_DIS\nefuse when Secure Boot is enabled. This prevents any more efuses from being read protected.\n\nIf this option is set, it will remain possible to write the EFUSE_RD_DIS efuse field after Secure\nBoot is enabled. This may allow an attacker to read-protect the BLK2 efuse (for ESP32) and\nBLOCK4-BLOCK10 (i.e. BLOCK_KEY0-BLOCK_KEY5)(for other chips) holding the public key digest, causing an\nimmediate denial of service and possibly allowing an additional fault injection attack to\nbypass the signature protection.\n\nNOTE: Once a BLOCK is read-protected, the application will read all zeros from that block\n\nNOTE: If \"UART ROM download mode (Permanently disabled (recommended))\" or\n\"UART ROM download mode (Permanently switch to Secure mode (recommended))\" is set,\nthen it is __NOT__ possible to read/write efuses using espefuse.py utility.\nHowever, efuse can be read/written from the application", "id": "SECURE_BOOT_V2_ALLOW_EFUSE_RD_DIS", "name": "SECURE_BOOT_V2_ALLOW_EFUSE_RD_DIS", "range": null, "title": "Allow additional read protecting of efuses", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_INSECURE && SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS", "help": "If not set (default), during startup in the app all unused digest slots will be revoked.\nTo revoke unused slot will be called esp_efuse_set_digest_revoke(num_digest) for each digest.\nRevoking unused digest slots makes ensures that no trusted keys can be added later by an attacker.\nIf set, it means that you have a plan to use unused digests slots later.", "id": "SECURE_BOOT_ALLOW_UNUSED_DIGEST_SLOTS", "name": "SECURE_BOOT_ALLOW_UNUSED_DIGEST_SLOTS", "range": null, "title": "Leave unused digest slots available (not revoke)", "type": "bool"}, {"children": [], "depends_on": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT", "help": "If not set (default), the bootloader will permanently disable UART bootloader encryption access on\nfirst boot. If set, the UART bootloader will still be able to access hardware encryption.\n\nIt is recommended to only set this option in testing environments.", "id": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_ENC", "name": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_ENC", "range": null, "title": "Leave UART bootloader encryption enabled", "type": "bool"}, {"children": [], "depends_on": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT && IDF_TARGET_ESP32", "help": "If not set (default), the bootloader will permanently disable UART bootloader decryption access on\nfirst boot. If set, the UART bootloader will still be able to access hardware decryption.\n\nOnly set this option in testing environments. Setting this option allows complete bypass of flash\nencryption.", "id": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_DEC", "name": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_DEC", "range": null, "title": "Leave UART bootloader decryption enabled", "type": "bool"}, {"children": [], "depends_on": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT && (IDF_TARGET_ESP32 || SOC_EFUSE_DIS_DOWNLOAD_ICACHE || SOC_EFUSE_DIS_DOWNLOAD_DCACHE)", "help": "If not set (default), the bootloader will permanently disable UART bootloader flash cache access on\nfirst boot. If set, the UART bootloader will still be able to access the flash cache.\n\nOnly set this option in testing environments.", "id": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_CACHE", "name": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_CACHE", "range": null, "title": "Leave UART bootloader flash cache enabled", "type": "bool"}, {"children": [], "depends_on": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT", "help": "If not set (default), and flash encryption is not yet enabled in eFuses, the 2nd stage bootloader\nwill enable flash encryption: generate the flash encryption key and program eFuses.\nIf this option is set, and flash encryption is not yet enabled, the bootloader will error out and\nreboot.\nIf flash encryption is enabled in eFuses, this option does not change the bootloader behavior.\n\nOnly use this option in testing environments, to avoid accidentally enabling flash encryption on\nthe wrong device. The device needs to have flash encryption already enabled using espefuse.py.", "id": "SECURE_FLASH_REQUIRE_ALREADY_ENABLED", "name": "SECURE_FLASH_REQUIRE_ALREADY_ENABLED", "range": null, "title": "Require flash encryption to be already enabled", "type": "bool"}, {"children": [], "depends_on": "SECURE_FLASH_HAS_WRITE_PROTECTION_CACHE", "help": "If not set (default, recommended), on the first boot the bootloader will burn the write-protection of\nDIS_CACHE(for ESP32) or DIS_ICACHE/DIS_DCACHE(for other chips) eFuse when Flash Encryption is enabled.\nWrite protection for cache disable efuse prevents the chip from being blocked if it is set by accident.\nApp and bootloader use cache so disabling it makes the chip useless for IDF.\nDue to other eFuses are linked with the same write protection bit (see the list below) then\nwrite-protection will not be done if these SECURE_FLASH_UART_BOOTLOADER_ALLOW_ENC,\nSECURE_BOOT_ALLOW_JTAG or SECURE_FLASH_UART_BOOTLOADER_ALLOW_CACHE options are selected\nto give a chance to turn on the chip into the release mode later.\n\nList of eFuses with the same write protection bit:\nESP32: MAC, MAC_CRC, DISABLE_APP_CPU, DISABLE_BT, DIS_CACHE, VOL_LEVEL_HP_INV.\n\nESP32-C3: DIS_ICACHE, DIS_USB_JTAG, DIS_DOWNLOAD_ICACHE, DIS_USB_SERIAL_JTAG,\nDIS_FORCE_DOWNLOAD, DIS_TWAI, JTAG_SEL_ENABLE, DIS_PAD_JTAG, DIS_DOWNLOAD_MANUAL_ENCRYPT.\n\nESP32-C6: SWAP_UART_SDIO_EN, DIS_ICACHE, DIS_USB_JTAG, DIS_DOWNLOAD_ICACHE,\nDIS_USB_SERIAL_JTAG, DIS_FORCE_DOWNLOAD, DIS_TWAI, JTAG_SEL_ENABLE,\nDIS_PAD_JTAG, DIS_DOWNLOAD_MANUAL_ENCRYPT.\n\nESP32-H2: DIS_ICACHE, DIS_USB_JTAG, POWERGLITCH_EN, DIS_FORCE_DOWNLOAD, SPI_DOWNLOAD_MSPI_DIS,\nDIS_TWAI, JTAG_SEL_ENABLE, DIS_PAD_JTAG, DIS_DOWNLOAD_MANUAL_ENCRYPT.\n\nESP32-S2: DIS_ICACHE, DIS_DCACHE, DIS_DOWNLOAD_ICACHE, DIS_DOWNLOAD_DCACHE,\nDIS_FORCE_DOWNLOAD, DIS_USB, DIS_TWAI, DIS_BOOT_REMAP, SOFT_DIS_JTAG,\nHARD_DIS_JTAG, DIS_DOWNLOAD_MANUAL_ENCRYPT.\n\nESP32-S3: DIS_ICACHE, DIS_DCACHE, DIS_DOWNLOAD_ICACHE, DIS_DOWNLOAD_DCACHE,\nDIS_FORCE_DOWNLOAD, DIS_USB_OTG, DIS_TWAI, DIS_APP_CPU, DIS_PAD_JTAG,\nDIS_DOWNLOAD_MANUAL_ENCRYPT, DIS_USB_JTAG, DIS_USB_SERIAL_JTAG, STRAP_JTAG_SEL, USB_PHY_SEL.", "id": "SECURE_FLASH_SKIP_WRITE_PROTECTION_CACHE", "name": "SECURE_FLASH_SKIP_WRITE_PROTECTION_CACHE", "range": null, "title": "Skip write-protection of DIS_CACHE (DIS_ICACHE, DIS_DCACHE)", "type": "bool"}], "depends_on": null, "id": "security-features-potentially-insecure-options", "title": "Potentially insecure options", "type": "menu"}, {"children": [], "depends_on": "SECURE_FLASH_ENC_ENABLED", "help": "If set (default), in an app during startup code,\nthere is a check of the flash encryption eFuse bit is on\n(as the bootloader should already have set it).\nThe app requires this bit is on to continue work otherwise abort.\n\nIf not set, the app does not care if the flash encryption eFuse bit is set or not.", "id": "SECURE_FLASH_CHECK_ENC_EN_IN_APP", "name": "SECURE_FLASH_CHECK_ENC_EN_IN_APP", "range": null, "title": "Check Flash Encryption enabled on app startup", "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "SECURE_ROM_DL_MODE_ENABLED", "name": "SECURE_ROM_DL_MODE_ENABLED", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice SECURE_UART_ROM_DL_MODE>", "help": "If set, during startup the app will burn an eFuse bit to permanently disable the UART ROM\nDownload Mode. This prevents any future use of esptool.py, espefuse.py and similar tools.\n\nOnce disabled, if the SoC is booted with strapping pins set for ROM Download Mode\nthen an error is printed instead.\n\nIt is recommended to enable this option in any production application where Flash\nEncryption and/or Secure Boot is enabled and access to Download Mode is not required.\n\nIt is also possible to permanently disable Download Mode by calling\nesp_efuse_disable_rom_download_mode() at runtime.", "id": "SECURE_DISABLE_ROM_DL_MODE", "name": "SECURE_DISABLE_ROM_DL_MODE", "range": null, "title": "UART ROM download mode (Permanently disabled (recommended))", "type": "bool"}, {"children": [], "depends_on": "SOC_SUPPORTS_SECURE_DL_MODE && <choice SECURE_UART_ROM_DL_MODE>", "help": "If set, during startup the app will burn an eFuse bit to permanently switch the UART ROM\nDownload Mode into a separate Secure Download mode. This option can only work if\nDownload Mode is not already disabled by eFuse.\n\nSecure Download mode limits the use of Download Mode functions to update SPI config,\nchanging baud rate, basic flash write and a command to return a summary of currently\nenabled security features (`get_security_info`).\n\nSecure Download mode is not compatible with the esptool.py flasher stub feature,\nespefuse.py, read/writing memory or registers, encrypted download, or any other\nfeatures that interact with unsupported Download Mode commands.\n\nSecure Download mode should be enabled in any application where Flash Encryption\nand/or Secure Boot is enabled. Disabling this option does not immediately cancel\nthe benefits of the security features, but it increases the potential \"attack\nsurface\" for an attacker to try and bypass them with a successful physical attack.\n\nIt is also possible to enable secure download mode at runtime by calling\nesp_efuse_enable_rom_secure_download_mode()\n\nNote: Secure Download mode is not available for ESP32 (includes revisions till ECO3).", "id": "SECURE_ENABLE_SECURE_ROM_DL_MODE", "name": "SECURE_ENABLE_SECURE_ROM_DL_MODE", "range": null, "title": "UART ROM download mode (Permanently switch to Secure mode (recommended))", "type": "bool"}, {"children": [], "depends_on": "<choice SECURE_UART_ROM_DL_MODE>", "help": "This is a potentially insecure option.\nEnabling this option will allow the full UART download mode to stay enabled.\nThis option SHOULD NOT BE ENABLED for production use cases.", "id": "SECURE_INSECURE_ALLOW_DL_MODE", "name": "SECURE_INSECURE_ALLOW_DL_MODE", "range": null, "title": "UART ROM download mode (Enabled (not recommended))", "type": "bool"}], "depends_on": "(SECURE_BOOT_V2_ENABLED || SECURE_FLASH_ENC_ENABLED) && !(IDF_TARGET_ESP32 && ESP32_REV_MIN_FULL < 300)", "help": null, "id": "security-features-uart-rom-download-mode", "name": "SECURE_UART_ROM_DL_MODE", "title": "UART ROM download mode", "type": "choice"}], "depends_on": null, "id": "security-features", "title": "Security features", "type": "menu"}, {"children": [{"children": [], "depends_on": null, "help": "If set, then the app will be built with the current time/date stamp. It is stored in the app description\nstructure. If not set, time/date stamp will be excluded from app image. This can be useful for getting the\nsame binary image files made from the same source, but at different times.", "id": "APP_COMPILE_TIME_DATE", "name": "APP_COMPILE_TIME_DATE", "range": null, "title": "Use time/date stamp for app", "type": "bool"}, {"children": [], "depends_on": null, "help": "The PROJECT_VER variable from the build system will not affect the firmware image.\nThis value will not be contained in the esp_app_desc structure.", "id": "APP_EXCLUDE_PROJECT_VER_VAR", "name": "APP_EXCLUDE_PROJECT_VER_VAR", "range": null, "title": "Exclude PROJECT_VER from firmware image", "type": "bool"}, {"children": [], "depends_on": null, "help": "The PROJECT_NAME variable from the build system will not affect the firmware image.\nThis value will not be contained in the esp_app_desc structure.", "id": "APP_EXCLUDE_PROJECT_NAME_VAR", "name": "APP_EXCLUDE_PROJECT_NAME_VAR", "range": null, "title": "Exclude PROJECT_NAME from firmware image", "type": "bool"}, {"children": [{"children": [], "depends_on": "APP_PROJECT_VER_FROM_CONFIG", "help": "Project version", "id": "APP_PROJECT_VER", "name": "APP_PROJECT_VER", "range": null, "title": "Project version", "type": "string"}], "depends_on": null, "help": "If this is enabled, then config item APP_PROJECT_VER will be used for the variable PROJECT_VER.\nOther ways to set PROJECT_VER will be ignored.", "id": "APP_PROJECT_VER_FROM_CONFIG", "name": "APP_PROJECT_VER_FROM_CONFIG", "range": null, "title": "Get the project version from Kconfig", "type": "bool"}, {"children": [], "depends_on": null, "help": "At startup, the app will read this many hex characters from the embedded APP ELF SHA-256 hash value\nand store it in static RAM. This ensures the app ELF SHA-256 value is always available\nif it needs to be printed by the panic handler code.\nChanging this value will change the size of a static buffer, in bytes.", "id": "APP_RETRIEVE_LEN_ELF_SHA", "name": "APP_RETRIEVE_LEN_ELF_SHA", "range": [8, 64], "title": "The length of APP ELF SHA is stored in RAM(chars)", "type": "int"}], "depends_on": null, "id": "application-manager", "title": "Application manager", "type": "menu"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_CRC_LE", "name": "ESP_ROM_HAS_CRC_LE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_CRC_BE", "name": "ESP_ROM_HAS_CRC_BE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_JPEG_DECODE", "name": "ESP_ROM_HAS_JPEG_DECODE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_UART_CLK_IS_XTAL", "name": "ESP_ROM_UART_CLK_IS_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_USB_SERIAL_DEVICE_NUM", "name": "ESP_ROM_USB_SERIAL_DEVICE_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_RETARGETABLE_LOCKING", "name": "ESP_ROM_HAS_RETARGETABLE_LOCKING", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_GET_CLK_FREQ", "name": "ESP_ROM_GET_CLK_FREQ", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_RVFPLIB", "name": "ESP_ROM_HAS_RVFPLIB", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_HAL_WDT", "name": "ESP_ROM_HAS_HAL_WDT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_HAL_SYSTIMER", "name": "ESP_ROM_HAS_HAL_SYSTIMER", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_HEAP_TLSF", "name": "ESP_ROM_HAS_HEAP_TLSF", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_LAYOUT_TABLE", "name": "ESP_ROM_HAS_LAYOUT_TABLE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_SPI_FLASH", "name": "ESP_ROM_HAS_SPI_FLASH", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_REGI2C_BUG", "name": "ESP_ROM_HAS_REGI2C_BUG", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_HAS_NEWLIB_NORMAL_FORMAT", "name": "ESP_ROM_HAS_NEWLIB_NORMAL_FORMAT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_REV0_HAS_NO_ECDSA_INTERFACE", "name": "ESP_ROM_REV0_HAS_NO_ECDSA_INTERFACE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_WDT_INIT_PATCH", "name": "ESP_ROM_WDT_INIT_PATCH", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_NEEDS_SET_CACHE_MMU_SIZE", "name": "ESP_ROM_NEEDS_SET_CACHE_MMU_SIZE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ROM_RAM_APP_NEEDS_MMU_INIT", "name": "ESP_ROM_RAM_APP_NEEDS_MMU_INIT", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [{"children": [], "depends_on": "<choice BOOT_ROM_LOG_SCHEME>", "help": "Always print ROM logs, this is the default behavior.", "id": "BOOT_ROM_LOG_ALWAYS_ON", "name": "BOOT_ROM_LOG_ALWAYS_ON", "range": null, "title": "Always Log", "type": "bool"}, {"children": [], "depends_on": "<choice BOOT_ROM_LOG_SCHEME>", "help": "Don't print ROM logs.", "id": "BOOT_ROM_LOG_ALWAYS_OFF", "name": "BOOT_ROM_LOG_ALWAYS_OFF", "range": null, "title": "Permanently disable logging", "type": "bool"}, {"children": [], "depends_on": "<choice BOOT_ROM_LOG_SCHEME>", "help": "Print ROM logs when GPIO level is high during start up.\nThe GPIO number is chip dependent,\ne.g. on ESP32-S2, the control GPIO is GPIO46.", "id": "BOOT_ROM_LOG_ON_GPIO_HIGH", "name": "BOOT_ROM_LOG_ON_GPIO_HIGH", "range": null, "title": "Log on GPIO High", "type": "bool"}, {"children": [], "depends_on": "<choice BOOT_ROM_LOG_SCHEME>", "help": "Print ROM logs when GPIO level is low during start up.\nThe GPIO number is chip dependent,\ne.g. on ESP32-S2, the control GPIO is GPIO46.", "id": "BOOT_ROM_LOG_ON_GPIO_LOW", "name": "BOOT_ROM_LOG_ON_GPIO_LOW", "range": null, "title": "Log on GPIO Low", "type": "bool"}], "depends_on": "!IDF_TARGET_ESP32", "help": "Controls the Boot ROM log behavior.\nThe rom log behavior can only be changed for once,\nspecific eFuse bit(s) will be burned at app boot stage.", "id": "boot-rom-behavior-permanently-change-boot-rom-output", "name": "BOOT_ROM_LOG_SCHEME", "title": "Permanently change Boot ROM output", "type": "choice"}], "depends_on": null, "id": "boot-rom-behavior", "title": "Boot ROM Behavior", "type": "menu"}, {"children": [{"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "The flasher tool sends a precompiled download stub first by default. That stub allows things\nlike compressed downloads and more. Usually you should not need to disable that feature", "id": "ESPTOOLPY_NO_STUB", "name": "ESPTOOLPY_NO_STUB", "range": null, "title": "Disable download stub", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32S3 && !APP_BUILD_TYPE_PURE_RAM_APP", "help": null, "id": "ESPTOOLPY_OCT_FLASH", "name": "ESPTOOLPY_OCT_FLASH", "range": null, "title": "Enable Octal Flash", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32S3 && !APP_BUILD_TYPE_PURE_RAM_APP", "help": "This config option helps decide whether flash is Quad or Octal, but please note some limitations:\n\n1. If the flash chip is an Octal one, even if one of \"QIO\", \"QOUT\", \"DIO\", \"DOUT\" options is\n   selected in `ESPTOOLPY_FLASHMODE`, our code will automatically change the\n   mode to \"<PERSON><PERSON>\" and the sample mode will be STR.\n2. If the flash chip is a Quad one, even if \"O<PERSON>\" is selected in `ESPTOOLPY_FLASHMODE`, our code will\n   automatically change the mode to \"DIO\".\n3. Please do not rely on this option when you are pretty sure that you are using Octal flash,\n   please enable `ESPTOOLPY_OCT_FLASH` option, then you can choose `DTR` sample mode\n   in `ESPTOOLPY_FLASH_SAMPLE_MODE`. Otherwise, only `STR` mode is available.\n4. Enabling this feature reduces available internal RAM size (around 900 bytes).\n   If your IRAM space is insufficient and you're aware of your flash type,\n   disable this option and select corresponding flash type options.", "id": "ESPTOOLPY_FLASH_MODE_AUTO_DETECT", "name": "ESPTOOLPY_FLASH_MODE_AUTO_DETECT", "range": null, "title": "Choose flash mode automatically (please read help)", "type": "bool"}, {"children": [{"children": [], "depends_on": "!ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASHMODE>", "help": null, "id": "ESPTOOLPY_FLASHMODE_QIO", "name": "ESPTOOLPY_FLASHMODE_QIO", "range": null, "title": "QIO", "type": "bool"}, {"children": [], "depends_on": "!ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASHMODE>", "help": null, "id": "ESPTOOLPY_FLASHMODE_QOUT", "name": "ESPTOOLPY_FLASHMODE_QOUT", "range": null, "title": "QOUT", "type": "bool"}, {"children": [], "depends_on": "!ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASHMODE>", "help": null, "id": "ESPTOOLPY_FLASHMODE_DIO", "name": "ESPTOOLPY_FLASHMODE_DIO", "range": null, "title": "DIO", "type": "bool"}, {"children": [], "depends_on": "!ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASHMODE>", "help": null, "id": "ESPTOOLPY_FLASHMODE_DOUT", "name": "ESPTOOLPY_FLASHMODE_DOUT", "range": null, "title": "DOUT", "type": "bool"}, {"children": [], "depends_on": "ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASHMODE>", "help": null, "id": "ESPTOOLPY_FLASHMODE_OPI", "name": "ESPTOOLPY_FLASHMODE_OPI", "range": null, "title": "OPI", "type": "bool"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Mode the flash chip is flashed in, as well as the default mode for the\nbinary to run in.", "id": "serial-flasher-config-flash-spi-mode", "name": "ESPTOOLPY_FLASHMODE", "title": "Flash SPI mode", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice ESPTOOLPY_FLASH_SAMPLE_MODE>", "help": null, "id": "ESPTOOLPY_FLASH_SAMPLE_MODE_STR", "name": "ESPTOOLPY_FLASH_SAMPLE_MODE_STR", "range": null, "title": "STR Mode", "type": "bool"}, {"children": [], "depends_on": "ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASH_SAMPLE_MODE>", "help": null, "id": "ESPTOOLPY_FLASH_SAMPLE_MODE_DTR", "name": "ESPTOOLPY_FLASH_SAMPLE_MODE_DTR", "range": null, "title": "DTR Mode", "type": "bool"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": null, "id": "serial-flasher-config-flash-sampling-mode", "name": "ESPTOOLPY_FLASH_SAMPLE_MODE", "title": "Flash Sampling Mode", "type": "choice"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": null, "id": "ESPTOOLPY_FLASHMODE", "name": "ESPTOOLPY_FLASHMODE", "range": null, "title": null, "type": "string"}, {"children": [{"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_120M && (ESPTOOLPY_FLASH_SAMPLE_MODE_STR || IDF_EXPERIMENTAL_FEATURES) && <choice ESPTOOLPY_FLASHFREQ>", "help": "- Flash 120 MHz SDR mode is stable.\n- Flash 120 MHz DDR mode is an experimental feature, it works when\n  the temperature is stable.\n\n    Risks:\n        If your chip powers on at a certain temperature, then after the temperature\n        increases or decreases by approximately 20 Celsius degrees (depending on the\n        chip), the program will crash randomly.", "id": "ESPTOOLPY_FLASHFREQ_120M", "name": "ESPTOOLPY_FLASHFREQ_120M", "range": null, "title": "120 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_80M", "name": "ESPTOOLPY_FLASHFREQ_80M", "range": null, "title": "80 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_64M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_64M", "name": "ESPTOOLPY_FLASHFREQ_64M", "range": null, "title": "64 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_60M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_60M", "name": "ESPTOOLPY_FLASHFREQ_60M", "range": null, "title": "60 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_48M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_48M", "name": "ESPTOOLPY_FLASHFREQ_48M", "range": null, "title": "48 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_40M", "name": "ESPTOOLPY_FLASHFREQ_40M", "range": null, "title": "40 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_32M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_32M", "name": "ESPTOOLPY_FLASHFREQ_32M", "range": null, "title": "32 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_30M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_30M", "name": "ESPTOOLPY_FLASHFREQ_30M", "range": null, "title": "30 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_26M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_26M", "name": "ESPTOOLPY_FLASHFREQ_26M", "range": null, "title": "26 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_24M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_24M", "name": "ESPTOOLPY_FLASHFREQ_24M", "range": null, "title": "24 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_20M", "name": "ESPTOOLPY_FLASHFREQ_20M", "range": null, "title": "20 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_16M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_16M", "name": "ESPTOOLPY_FLASHFREQ_16M", "range": null, "title": "16 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_MEMSPI_SRC_FREQ_15M_SUPPORTED && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_15M", "name": "ESPTOOLPY_FLASHFREQ_15M", "range": null, "title": "15 MHz", "type": "bool"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": null, "id": "serial-flasher-config-flash-spi-speed", "name": "ESPTOOLPY_FLASHFREQ", "title": "Flash SPI speed", "type": "choice"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "This is an invisible item, used to define the targets that defaults to use 80MHz Flash SPI speed.", "id": "ESPTOOLPY_FLASHFREQ_80M_DEFAULT", "name": "ESPTOOLPY_FLASHFREQ_80M_DEFAULT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": null, "id": "ESPTOOLPY_FLASHFREQ", "name": "ESPTOOLPY_FLASHFREQ", "range": null, "title": null, "type": "string"}, {"children": [{"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_1MB", "name": "ESPTOOLPY_FLASHSIZE_1MB", "range": null, "title": "1 MB", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_2MB", "name": "ESPTOOLPY_FLASHSIZE_2MB", "range": null, "title": "2 MB", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_4MB", "name": "ESPTOOLPY_FLASHSIZE_4MB", "range": null, "title": "4 MB", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_8MB", "name": "ESPTOOLPY_FLASHSIZE_8MB", "range": null, "title": "8 MB", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_16MB", "name": "ESPTOOLPY_FLASHSIZE_16MB", "range": null, "title": "16 MB", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_32MB", "name": "ESPTOOLPY_FLASHSIZE_32MB", "range": null, "title": "32 MB", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_64MB", "name": "ESPTOOLPY_FLASHSIZE_64MB", "range": null, "title": "64 MB", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_128MB", "name": "ESPTOOLPY_FLASHSIZE_128MB", "range": null, "title": "128 MB", "type": "bool"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "SPI flash size, in megabytes", "id": "serial-flasher-config-flash-size", "name": "ESPTOOLPY_FLASHSIZE", "title": "Flash size", "type": "choice"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": null, "id": "ESPTOOLPY_FLASHSIZE", "name": "ESPTOOLPY_FLASHSIZE", "range": null, "title": null, "type": "string"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "If this option is set, flashing the project will automatically detect\nthe flash size of the target chip and update the bootloader image\nbefore it is flashed.\n\nEnabling this option turns off the image protection against corruption\nby a SHA256 digest. Updating the bootloader image before flashing would\ninvalidate the digest.", "id": "ESPTOOLPY_HEADER_FLASHSIZE_UPDATE", "name": "ESPTOOLPY_HEADER_FLASHSIZE_UPDATE", "range": null, "title": "Detect flash size when flashing bootloader", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice ESPTOOLPY_BEFORE>", "help": null, "id": "ESPTOOLPY_BEFORE_RESET", "name": "ESPTOOLPY_BEFORE_RESET", "range": null, "title": "Reset to bootloader", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_BEFORE>", "help": null, "id": "ESPTOOLPY_BEFORE_NORESET", "name": "ESPTOOLPY_BEFORE_NORESET", "range": null, "title": "No reset", "type": "bool"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Configure whether esptool.py should reset the ESP32 before flashing.\n\nAutomatic resetting depends on the RTS & DTR signals being\nwired from the serial port to the ESP32. Most USB development\nboards do this internally.", "id": "serial-flasher-config-before-flashing", "name": "ESPTOOLPY_BEFORE", "title": "Before flashing", "type": "choice"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": null, "id": "ESPTOOLPY_BEFORE", "name": "ESPTOOLPY_BEFORE", "range": null, "title": null, "type": "string"}, {"children": [{"children": [], "depends_on": "<choice ESPTOOLPY_AFTER>", "help": null, "id": "ESPTOOLPY_AFTER_RESET", "name": "ESPTOOLPY_AFTER_RESET", "range": null, "title": "Reset after flashing", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_AFTER>", "help": null, "id": "ESPTOOLPY_AFTER_NORESET", "name": "ESPTOOLPY_AFTER_NORESET", "range": null, "title": "Stay in bootloader", "type": "bool"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Configure whether esptool.py should reset the ESP32 after flashing.\n\nAutomatic resetting depends on the RTS & DTR signals being\nwired from the serial port to the ESP32. Most USB development\nboards do this internally.", "id": "serial-flasher-config-after-flashing", "name": "ESPTOOLPY_AFTER", "title": "After flashing", "type": "choice"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": null, "id": "ESPTOOLPY_AFTER", "name": "ESPTOOLPY_AFTER", "range": null, "title": null, "type": "string"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": null, "id": "ESPTOOLPY_MONITOR_BAUD", "name": "ESPTOOLPY_MONITOR_BAUD", "range": null, "title": null, "type": "int"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "id": "serial-flasher-config", "title": "Serial flasher config", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice PARTITION_TABLE_TYPE>", "help": "This is the default partition table, designed to fit into a 2MB or\nlarger flash with a single 1MB app partition.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_singleapp.csv\n\nThis partition table is not suitable for an app that needs OTA\n(over the air update) capability.", "id": "PARTITION_TABLE_SINGLE_APP", "name": "PARTITION_TABLE_SINGLE_APP", "range": null, "title": "Single factory app, no OTA", "type": "bool"}, {"children": [], "depends_on": "<choice PARTITION_TABLE_TYPE>", "help": "This is a variation of the default partition table, that expands\nthe 1MB app partition size to 1.5MB to fit more code.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_singleapp_large.csv\n\nThis partition table is not suitable for an app that needs OTA\n(over the air update) capability.", "id": "PARTITION_TABLE_SINGLE_APP_LARGE", "name": "PARTITION_TABLE_SINGLE_APP_LARGE", "range": null, "title": "Single factory app (large), no OTA", "type": "bool"}, {"children": [], "depends_on": "<choice PARTITION_TABLE_TYPE>", "help": "This is a basic OTA-enabled partition table with a factory app\npartition plus two OTA app partitions. All are 1MB, so this\npartition table requires 4MB or larger flash size.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_two_ota.csv", "id": "PARTITION_TABLE_TWO_OTA", "name": "PARTITION_TABLE_TWO_OTA", "range": null, "title": "Factory app, two OTA definitions", "type": "bool"}, {"children": [], "depends_on": "<choice PARTITION_TABLE_TYPE>", "help": "Specify the path to the partition table CSV to use for your project.\n\nConsult the Partition Table section in the ESP-IDF Programmers Guide\nfor more information.", "id": "PARTITION_TABLE_CUSTOM", "name": "PARTITION_TABLE_CUSTOM", "range": null, "title": "Custom partition table CSV", "type": "bool"}, {"children": [], "depends_on": "!ESP32_COREDUMP_ENABLE_TO_FLASH && NVS_ENCRYPTION && <choice PARTITION_TABLE_TYPE>", "help": "This is a variation of the default \"Single factory app, no OTA\" partition table\nthat supports encrypted NVS when using flash encryption. See the Flash Encryption section\nin the ESP-IDF Programmers Guide for more information.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_singleapp_encr_nvs.csv", "id": "PARTITION_TABLE_SINGLE_APP_ENCRYPTED_NVS", "name": "PARTITION_TABLE_SINGLE_APP_ENCRYPTED_NVS", "range": null, "title": "Single factory app, no OTA, encrypted NVS", "type": "bool"}, {"children": [], "depends_on": "!ESP32_COREDUMP_ENABLE_TO_FLASH && NVS_ENCRYPTION && <choice PARTITION_TABLE_TYPE>", "help": "This is a variation of the \"Single factory app (large), no OTA\" partition table\nthat supports encrypted NVS when using flash encryption. See the Flash Encryption section\nin the ESP-IDF Programmers Guide for more information.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_singleapp_large_encr_nvs.csv", "id": "PARTITION_TABLE_SINGLE_APP_LARGE_ENC_NVS", "name": "PARTITION_TABLE_SINGLE_APP_LARGE_ENC_NVS", "range": null, "title": "Single factory app (large), no OTA, encrypted NVS", "type": "bool"}, {"children": [], "depends_on": "!ESP_COREDUMP_ENABLE_TO_FLASH && NVS_ENCRYPTION && <choice PARTITION_TABLE_TYPE>", "help": "This is a variation of the \"Factory app, two OTA definitions\" partition table\nthat supports encrypted NVS when using flash encryption. See the Flash Encryption section\nin the ESP-IDF Programmers Guide for more information.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_two_ota_encr_nvs.csv", "id": "PARTITION_TABLE_TWO_OTA_ENCRYPTED_NVS", "name": "PARTITION_TABLE_TWO_OTA_ENCRYPTED_NVS", "range": null, "title": "Factory app, two OTA definitions, encrypted NVS", "type": "bool"}], "depends_on": null, "help": "The partition table to flash to the ESP32. The partition table\ndetermines where apps, data and other resources are expected to\nbe found.\n\nThe predefined partition table CSV descriptions can be found\nin the components/partition_table directory. These are mostly intended\nfor example and development use, it's expect that for production use you\nwill copy one of these CSV files and create a custom partition CSV for\nyour application.", "id": "partition-table-partition-table", "name": "PARTITION_TABLE_TYPE", "title": "Partition Table", "type": "choice"}, {"children": [], "depends_on": null, "help": "Name of the custom partition CSV filename. This path is evaluated\nrelative to the project root directory.", "id": "PARTITION_TABLE_CUSTOM_FILENAME", "name": "PARTITION_TABLE_CUSTOM_FILENAME", "range": null, "title": "Custom partition CSV file", "type": "string"}, {"children": [], "depends_on": null, "help": null, "id": "PARTITION_TABLE_FILENAME", "name": "PARTITION_TABLE_FILENAME", "range": null, "title": null, "type": "string"}, {"children": [], "depends_on": null, "help": "The address of partition table (by default 0x8000).\nAllows you to move the partition table, it gives more space for the bootloader.\nNote that the bootloader and app will both need to be compiled with the same PARTITION_TABLE_OFFSET value.\n\nThis number should be a multiple of 0x1000.\n\nNote that partition offsets in the partition table CSV file may need to be changed if this value is set to\na higher value. To have each partition offset adapt to the configured partition table offset, leave all\npartition offsets blank in the CSV file.", "id": "PARTITION_TABLE_OFFSET", "name": "PARTITION_TABLE_OFFSET", "range": null, "title": "Offset of partition table", "type": "hex"}, {"children": [], "depends_on": "!APP_COMPATIBLE_PRE_V3_1_BOOTLOADERS && !IDF_TARGET_LINUX", "help": "Generate an MD5 checksum for the partition table for protecting the\nintegrity of the table. The generation should be turned off for legacy\nbootloaders which cannot recognize the MD5 checksum in the partition\ntable.", "id": "PARTITION_TABLE_MD5", "name": "PARTITION_TABLE_MD5", "range": null, "title": "Generate an MD5 checksum for the partition table", "type": "bool"}], "depends_on": null, "id": "partition-table", "title": "Partition Table", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION>", "help": null, "id": "COMPILER_OPTIMIZATION_DEFAULT", "name": "COMPILER_OPTIMIZATION_DEFAULT", "range": null, "title": "Debug (-Og)", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION>", "help": null, "id": "COMPILER_OPTIMIZATION_SIZE", "name": "COMPILER_OPTIMIZATION_SIZE", "range": null, "title": "Optimize for size (-Os)", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION>", "help": null, "id": "COMPILER_OPTIMIZATION_PERF", "name": "COMPILER_OPTIMIZATION_PERF", "range": null, "title": "Optimize for performance (-O2)", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION>", "help": null, "id": "COMPILER_OPTIMIZATION_NONE", "name": "COMPILER_OPTIMIZATION_NONE", "range": null, "title": "Debug without optimization (-O0)", "type": "bool"}], "depends_on": null, "help": "This option sets compiler optimization level (gcc -O argument) for the app.\n\n- The \"Default\" setting will add the -0g flag to CFLAGS.\n- The \"Size\" setting will add the -0s flag to CFLAGS.\n- The \"Performance\" setting will add the -O2 flag to CFLAGS.\n- The \"None\" setting will add the -O0 flag to CFLAGS.\n\nThe \"Size\" setting cause the compiled code to be smaller and faster, but\nmay lead to difficulties of correlating code addresses to source file\nlines when debugging.\n\nThe \"Performance\" setting causes the compiled code to be larger and faster,\nbut will be easier to correlated code addresses to source file lines.\n\n\"None\" with -O0 produces compiled code without optimization.\n\nNote that custom optimization levels may be unsupported.\n\nCompiler optimization for the IDF bootloader is set separately,\nsee the BOOTLOADER_COMPILER_OPTIMIZATION setting.", "id": "compiler-options-optimization-level", "name": "COMPILER_OPTIMIZATION", "title": "Optimization Level", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION_ASSERTION_LEVEL>", "help": "Enable assertions. Assertion content and line number will be printed on failure.", "id": "COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE", "name": "COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE", "range": null, "title": "Enabled", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION_ASSERTION_LEVEL>", "help": "Enable silent assertions. Failed assertions will abort(), user needs to\nuse the aborting address to find the line number with the failed assertion.", "id": "COMPILER_OPTIMIZATION_ASSERTIONS_SILENT", "name": "COMPILER_OPTIMIZATION_ASSERTIONS_SILENT", "range": null, "title": "Silent (saves code size)", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION_ASSERTION_LEVEL>", "help": "If assertions are disabled, -DNDEBUG is added to CPPFLAGS.", "id": "COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE", "name": "COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE", "range": null, "title": "Disabled (sets -DNDEBUG)", "type": "bool"}], "depends_on": null, "help": "Assertions can be:\n\n- Enabled. Failure will print verbose assertion details. This is the default.\n\n- Set to \"silent\" to save code size (failed assertions will abort() but user\n  needs to use the aborting address to find the line number with the failed assertion.)\n\n- Disabled entirely (not recommended for most configurations.) -DNDEBUG is added\n  to CPPFLAGS in this case.", "id": "compiler-options-assertion-level", "name": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL", "title": "Assertion level", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice COMPILER_FLOAT_LIB_FROM>", "help": null, "id": "COMPILER_FLOAT_LIB_FROM_GCCLIB", "name": "COMPILER_FLOAT_LIB_FROM_GCCLIB", "range": null, "title": "libgcc", "type": "bool"}, {"children": [], "depends_on": "ESP_ROM_HAS_RVFPLIB && <choice COMPILER_FLOAT_LIB_FROM>", "help": null, "id": "COMPILER_FLOAT_LIB_FROM_RVFPLIB", "name": "COMPILER_FLOAT_LIB_FROM_RVFPLIB", "range": null, "title": "librvfp", "type": "bool"}], "depends_on": null, "help": "In the soft-fp part of libgcc, riscv version is written in C,\nand handles all edge cases in IEEE754, which makes it larger\nand performance is slow.\n\nRVfplib is an optimized RISC-V library for FP arithmetic on 32-bit\ninteger processors, for single and double-precision FP.\nRVfplib is \"fast\", but it has a few exceptions from IEEE 754 compliance.", "id": "compiler-options-compiler-float-lib-source", "name": "COMPILER_FLOAT_LIB_FROM", "title": "Compiler float lib source", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL", "name": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": "If enabled, the error messages will be discarded in following check macros:\n- ESP_RETURN_ON_ERROR\n- ESP_EXIT_ON_ERROR\n- ESP_RETURN_ON_FALSE\n- ESP_EXIT_ON_FALSE", "id": "COMPILER_OPTIMIZATION_CHECKS_SILENT", "name": "COMPILER_OPTIMIZATION_CHECKS_SILENT", "range": null, "title": "Disable messages in ESP_RETURN_ON_* and ESP_EXIT_ON_* macros", "type": "bool"}, {"children": [], "depends_on": null, "help": "When expanding the __FILE__ and __BASE_FILE__ macros, replace paths inside ESP-IDF\nwith paths relative to the placeholder string \"IDF\", and convert paths inside the\nproject directory to relative paths.\n\nThis allows building the project with assertions or other code that embeds file paths,\nwithout the binary containing the exact path to the IDF or project directories.\n\nThis option passes -fmacro-prefix-map options to the GCC command line. To replace additional\npaths in your binaries, modify the project CMakeLists.txt file to pass custom -fmacro-prefix-map or\n-ffile-prefix-map arguments.", "id": "COMPILER_HIDE_PATHS_MACROS", "is_menuconfig": true, "name": "COMPILER_HIDE_PATHS_MACROS", "range": null, "title": "Replace ESP-IDF and project paths in binaries", "type": "menu"}, {"children": [{"children": [], "depends_on": "COMPILER_CXX_EXCEPTIONS", "help": "Size (in bytes) of the emergency memory pool for C++ exceptions. This pool will be used to allocate\nmemory for thrown exceptions when there is not enough memory on the heap.", "id": "COMPILER_CXX_EXCEPTIONS_EMG_POOL_SIZE", "name": "COMPILER_CXX_EXCEPTIONS_EMG_POOL_SIZE", "range": null, "title": "Emergency Pool Size", "type": "int"}], "depends_on": null, "help": "Enabling this option compiles all IDF C++ files with exception support enabled.\n\nDisabling this option disables C++ exception support in all compiled files, and any libstdc++ code\nwhich throws an exception will abort instead.\n\nEnabling this option currently adds an additional ~500 bytes of heap overhead\nwhen an exception is thrown in user code for the first time.", "id": "COMPILER_CXX_EXCEPTIONS", "is_menuconfig": true, "name": "COMPILER_CXX_EXCEPTIONS", "range": null, "title": "Enable C++ exceptions", "type": "menu"}, {"children": [], "depends_on": null, "help": "Enabling this option compiles all C++ files with RTTI support enabled.\nThis increases binary size (typically by tens of kB) but allows using\ndynamic_cast conversion and typeid operator.", "id": "COMPILER_CXX_RTTI", "name": "COMPILER_CXX_RTTI", "range": null, "title": "Enable C++ run-time type info (RTTI)", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice COMPILER_STACK_CHECK_MODE>", "help": null, "id": "COMPILER_STACK_CHECK_MODE_NONE", "name": "COMPILER_STACK_CHECK_MODE_NONE", "range": null, "title": "None", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_STACK_CHECK_MODE>", "help": null, "id": "COMPILER_STACK_CHECK_MODE_NORM", "name": "COMPILER_STACK_CHECK_MODE_NORM", "range": null, "title": "Normal", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_STACK_CHECK_MODE>", "help": null, "id": "COMPILER_STACK_CHECK_MODE_STRONG", "name": "COMPILER_STACK_CHECK_MODE_STRONG", "range": null, "title": "Strong", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_STACK_CHECK_MODE>", "help": null, "id": "COMPILER_STACK_CHECK_MODE_ALL", "name": "COMPILER_STACK_CHECK_MODE_ALL", "range": null, "title": "Overall", "type": "bool"}], "depends_on": null, "help": "Stack smashing protection mode. Emit extra code to check for buffer overflows, such as stack\nsmashing attacks. This is done by adding a guard variable to functions with vulnerable objects.\nThe guards are initialized when a function is entered and then checked when the function exits.\nIf a guard check fails, program is halted. Protection has the following modes:\n\n- In NORMAL mode (GCC flag: -fstack-protector) only functions that call alloca, and functions with\n  buffers larger than 8 bytes are protected.\n\n- STRONG mode (GCC flag: -fstack-protector-strong) is like NORMAL, but includes additional functions\n  to be protected -- those that have local array definitions, or have references to local frame\n  addresses.\n\n- In OVERALL mode (GCC flag: -fstack-protector-all) all functions are protected.\n\nModes have the following impact on code performance and coverage:\n\n- performance: NORMAL > STRONG > OVERALL\n\n- coverage: NORMAL < STRONG < OVERALL\n\nThe performance impact includes increasing the amount of stack memory required for each task.", "id": "compiler-options-stack-smashing-protection-mode", "name": "COMPILER_STACK_CHECK_MODE", "title": "Stack smashing protection mode", "type": "choice"}, {"children": [], "depends_on": null, "help": "Stack smashing protection.", "id": "COMPILER_STACK_CHECK", "name": "COMPILER_STACK_CHECK", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": "Adds -Wwrite-strings flag for the C/C++ compilers.\n\nFor C, this gives string constants the type ``const char[]`` so that\ncopying the address of one into a non-const ``char *`` pointer\nproduces a warning. This warning helps to find at compile time code\nthat tries to write into a string constant.\n\nFor C++, this warns about the deprecated conversion from string\nliterals to ``char *``.", "id": "COMPILER_WARN_WRITE_STRINGS", "name": "COMPILER_WARN_WRITE_STRINGS", "range": null, "title": "Enable -Wwrite-strings warning flag", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ARCH_RISCV", "help": "Adds -msave-restore to C/C++ compilation flags.\n\nWhen this flag is enabled, compiler will call library functions to\nsave/restore registers in function prologues/epilogues. This results\nin lower overall code size, at the expense of slightly reduced performance.\n\nThis option can be enabled for RISC-V targets only.", "id": "COMPILER_SAVE_RESTORE_LIBCALLS", "name": "COMPILER_SAVE_RESTORE_LIBCALLS", "range": null, "title": "Enable -msave-restore flag to reduce code size", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enable this option if use GCC 12 or newer, and want to disable warnings which don't appear with\nGCC 11.", "id": "COMPILER_DISABLE_GCC12_WARNINGS", "name": "COMPILER_DISABLE_GCC12_WARNINGS", "range": null, "title": "Disable new warnings introduced in GCC 12", "type": "bool"}, {"children": [], "depends_on": null, "help": "If enabled, RTL files will be produced during compilation. These files\ncan be used by other tools, for example to calculate call graphs.", "id": "COMPILER_DUMP_RTL_FILES", "name": "COMPILER_DUMP_RTL_FILES", "range": null, "title": "Dump RTL files during compilation", "type": "bool"}], "depends_on": null, "id": "compiler-options", "title": "Compiler options", "type": "menu"}, {"children": [{"children": [{"children": [{"children": [], "depends_on": "EFUSE_CUSTOM_TABLE", "help": "Name of the custom eFuse CSV filename. This path is evaluated\nrelative to the project root directory.", "id": "EFUSE_CUSTOM_TABLE_FILENAME", "name": "EFUSE_CUSTOM_TABLE_FILENAME", "range": null, "title": "Custom eFuse CSV file", "type": "string"}], "depends_on": null, "help": "Allows to generate a structure for eFuse from the CSV file.", "id": "EFUSE_CUSTOM_TABLE", "name": "EFUSE_CUSTOM_TABLE", "range": null, "title": "Use custom eFuse table", "type": "bool"}, {"children": [{"children": [], "depends_on": "EFUSE_VIRTUAL", "help": "In addition to the \"Simulate eFuse operations in RAM\" option, this option just adds\na feature to keep eFuses after reboots in flash memory. To use this mode the partition_table\nshould have the `efuse` partition. partition.csv: \"efuse_em, data, efuse,   ,   0x2000,\"\n\nDuring startup, the eFuses are copied from flash or,\nin case if flash is empty, from real eFuse to RAM and then update flash.\nThis mode is useful when need to keep changes after reboot\n(testing secure_boot and flash_encryption).", "id": "EFUSE_VIRTUAL_KEEP_IN_FLASH", "name": "EFUSE_VIRTUAL_KEEP_IN_FLASH", "range": null, "title": "Keep eFuses in flash", "type": "bool"}, {"children": [], "depends_on": "EFUSE_VIRTUAL", "help": "If enabled, log efuse burns. This shows changes that would be made.", "id": "EFUSE_VIRTUAL_LOG_ALL_WRITES", "name": "EFUSE_VIRTUAL_LOG_ALL_WRITES", "range": null, "title": "Log all virtual writes", "type": "bool"}], "depends_on": null, "help": "If \"n\" - No virtual mode. All eFuse operations are real and use eFuse registers.\nIf \"y\" - The virtual mode is enabled and all eFuse operations (read and write) are redirected\nto RAM instead of eFuse registers, all permanent changes (via eFuse) are disabled.\nLog output will state changes that would be applied, but they will not be.\n\nIf it is \"y\", then SECURE_FLASH_ENCRYPTION_MODE_RELEASE cannot be used.\nBecause the EFUSE VIRT mode is for testing only.\n\nDuring startup, the eFuses are copied into RAM. This mode is useful for fast tests.", "id": "EFUSE_VIRTUAL", "name": "EFUSE_VIRTUAL", "range": null, "title": "Simulate eFuse operations in RAM", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice EFUSE_CODE_SCHEME_SELECTOR>", "help": null, "id": "EFUSE_CODE_SCHEME_COMPAT_NONE", "name": "EFUSE_CODE_SCHEME_COMPAT_NONE", "range": null, "title": "None Only", "type": "bool"}, {"children": [], "depends_on": "<choice EFUSE_CODE_SCHEME_SELECTOR>", "help": null, "id": "EFUSE_CODE_SCHEME_COMPAT_3_4", "name": "EFUSE_CODE_SCHEME_COMPAT_3_4", "range": null, "title": "3/4 and None", "type": "bool"}, {"children": [], "depends_on": "<choice EFUSE_CODE_SCHEME_SELECTOR>", "help": null, "id": "EFUSE_CODE_SCHEME_COMPAT_REPEAT", "name": "EFUSE_CODE_SCHEME_COMPAT_REPEAT", "range": null, "title": "Repeat, 3/4 and None (common table does not support it)", "type": "bool"}], "depends_on": "IDF_TARGET_ESP32", "help": "Selector eFuse code scheme.", "id": "component-config-efuse-bit-manager-coding-scheme-compatibility", "name": "EFUSE_CODE_SCHEME_SELECTOR", "title": "Coding Scheme Compatibility", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "EFUSE_MAX_BLK_LEN", "name": "EFUSE_MAX_BLK_LEN", "range": null, "title": null, "type": "int"}], "depends_on": null, "id": "component-config-efuse-bit-manager", "title": "eFuse Bit Manager", "type": "menu"}, {"children": [{"children": [], "depends_on": null, "help": "Functions esp_err_to_name() and esp_err_to_name_r() return string representations of error codes from a\npre-generated lookup table. This option can be used to turn off the use of the look-up table in order to\nsave memory but this comes at the price of sacrificing distinguishable (meaningful) output string\nrepresentations.", "id": "ESP_ERR_TO_NAME_LOOKUP", "name": "ESP_ERR_TO_NAME_LOOKUP", "range": null, "title": "Enable lookup of error code strings", "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ALLOW_BSS_SEG_EXTERNAL_MEMORY", "name": "ESP_ALLOW_BSS_SEG_EXTERNAL_MEMORY", "range": null, "title": null, "type": "bool"}], "depends_on": null, "id": "component-config-common-esp-related", "title": "Common ESP-related", "type": "menu"}, {"children": [{"children": [{"children": [{"children": [], "depends_on": "<choice ESP32C6_REV_MIN>", "help": null, "id": "ESP32C6_REV_MIN_0", "name": "ESP32C6_REV_MIN_0", "range": null, "title": "Rev v0.0", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C6_REV_MIN>", "help": null, "id": "ESP32C6_REV_MIN_1", "name": "ESP32C6_REV_MIN_1", "range": null, "title": "Rev v0.1 (ECO1)", "type": "bool"}], "depends_on": null, "help": "Required minimum chip revision. ESP-IDF will check for it and\nreject to boot if the chip revision fails the check.\nThis ensures the chip used will have some modifications (features, or bugfixes).\n\nThe complied binary will only support chips above this revision,\nthis will also help to reduce binary size.", "id": "component-config-hardware-settings-chip-revision-minimum-supported-esp32-c6-revision", "name": "ESP32C6_REV_MIN", "title": "Minimum Supported ESP32-C6 Revision", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP32C6_REV_MIN_FULL", "name": "ESP32C6_REV_MIN_FULL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_REV_MIN_FULL", "name": "ESP_REV_MIN_FULL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "ESP32C6_REV_MAX_FULL", "name": "ESP32C6_REV_MAX_FULL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_REV_MAX_FULL", "name": "ESP_REV_MAX_FULL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": "IDF_CI_BUILD", "help": "For internal chip testing, a small number of new versions chips didn't\nupdate the version field in eFuse, you can enable this option to force the\nsoftware recognize the chip version based on the rev selected in menuconfig.", "id": "ESP_REV_NEW_CHIP_TEST", "name": "ESP_REV_NEW_CHIP_TEST", "range": null, "title": "Internal test mode", "type": "bool"}], "depends_on": null, "id": "component-config-hardware-settings-chip-revision", "title": "Chip revision", "type": "menu"}, {"children": [{"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_ADDR_UNIVERSE_WIFI_STA", "name": "ESP_MAC_ADDR_UNIVERSE_WIFI_STA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_ADDR_UNIVERSE_WIFI_AP", "name": "ESP_MAC_ADDR_UNIVERSE_WIFI_AP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_ADDR_UNIVERSE_BT", "name": "ESP_MAC_ADDR_UNIVERSE_BT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_ADDR_UNIVERSE_ETH", "name": "ESP_MAC_ADDR_UNIVERSE_ETH", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_ADDR_UNIVERSE_IEEE802154", "name": "ESP_MAC_ADDR_UNIVERSE_IEEE802154", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_ONE", "name": "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_ONE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_TWO", "name": "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_TWO", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR", "name": "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice ESP32C6_UNIVERSAL_MAC_ADDRESSES>", "help": null, "id": "ESP32C6_UNIVERSAL_MAC_ADDRESSES_TWO", "name": "ESP32C6_UNIVERSAL_MAC_ADDRESSES_TWO", "range": null, "title": "Two", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C6_UNIVERSAL_MAC_ADDRESSES>", "help": null, "id": "ESP32C6_UNIVERSAL_MAC_ADDRESSES_FOUR", "name": "ESP32C6_UNIVERSAL_MAC_ADDRESSES_FOUR", "range": null, "title": "Four", "type": "bool"}], "depends_on": null, "help": "Configure the number of universally administered (by IEEE) MAC addresses.\n\nDuring initialization, MAC addresses for each network interface are generated or derived from a\nsingle base MAC address.\n\nIf the number of universal MAC addresses is four, all four interfaces (WiFi station, WiFi softap,\nBluetooth and Ethernet) receive a universally administered MAC address. These are generated\nsequentially by adding 0, 1, 2 and 3 (respectively) to the final octet of the base MAC address.\n\nIf the number of universal MAC addresses is two, only two interfaces (WiFi station and Bluetooth)\nreceive a universally administered MAC address. These are generated sequentially by adding 0\nand 1 (respectively) to the base MAC address. The remaining two interfaces (WiFi softap and Ethernet)\nreceive local MAC addresses. These are derived from the universal WiFi station and Bluetooth MAC\naddresses, respectively.\n\nWhen using the default (Espressif-assigned) base MAC address, either setting can be used. When using\na custom universal MAC address range, the correct setting will depend on the allocation of MAC\naddresses in this range (either 2 or 4 per device.)\n\nNote that ESP32-C6 has no integrated Ethernet MAC. Although it's possible to use the esp_read_mac()\nAPI to return a MAC for Ethernet, this can only be used with an external MAC peripheral.", "id": "component-config-hardware-settings-mac-config-number-of-universally-administered-by-ieee-mac-address", "name": "ESP32C6_UNIVERSAL_MAC_ADDRESSES", "title": "Number of universally administered (by IEEE) MAC address", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP32C6_UNIVERSAL_MAC_ADDRESSES", "name": "ESP32C6_UNIVERSAL_MAC_ADDRESSES", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": "IDF_TARGET_ESP32", "help": "If you have an invalid MAC CRC (ESP_ERR_INVALID_CRC) problem\nand you still want to use this chip, you can enable this option to bypass such an error.\nThis applies to both MAC_FACTORY and CUSTOM_MAC efuses.", "id": "ESP_MAC_IGNORE_MAC_CRC_ERROR", "name": "ESP_MAC_IGNORE_MAC_CRC_ERROR", "range": null, "title": "Ignore MAC CRC error (not recommended)", "type": "bool"}], "depends_on": null, "id": "component-config-hardware-settings-mac-config", "title": "MAC Config", "type": "menu"}, {"children": [{"children": [], "depends_on": "!SPIRAM", "help": "If enabled, chip will try to power down flash as part of esp_light_sleep_start(), which costs\nmore time when chip wakes up. Can only be enabled if there is no SPIRAM configured.\n\nThis option will power down flash under a strict but relatively safe condition. Also, it is possible to\npower down flash under a relaxed condition by using esp_sleep_pd_config() to set ESP_PD_DOMAIN_VDDSDIO\nto ESP_PD_OPTION_OFF. It should be noted that there is a risk in powering down flash, you can refer\n`ESP-IDF Programming Guide/API Reference/System API/Sleep Modes/Power-down of Flash` for more details.", "id": "ESP_SLEEP_POWER_DOWN_FLASH", "name": "ESP_SLEEP_POWER_DOWN_FLASH", "range": null, "title": "Power down flash in light sleep when there is no SPIRAM", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP && !ESP_SLEEP_POWER_DOWN_FLASH", "help": "All IOs will be set to isolate(floating) state by default during sleep.\nSince the power supply of SPI Flash is not lost during lightsleep, if its CS pin is recognized as\nlow level(selected state) in the floating state, there will be a large current leakage, and the\ndata in Flash may be corrupted by random signals on other SPI pins.\nSelect this option will set the CS pin of Flash to PULL-UP state during sleep, but this will\nincrease the sleep current about 10 uA.\nIf you are developing with esp32xx modules, you must select this option, but if you are developing\nwith chips, you can also pull up the CS pin of SPI Flash in the external circuit to save power\nconsumption caused by internal pull-up during sleep.\n(!!! Don't deselect this option if you don't have external SPI Flash CS pin pullups.)", "id": "ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND", "name": "ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND", "range": null, "title": "Pull-up Flash CS pin in light sleep", "type": "bool"}, {"children": [], "depends_on": "SPIRAM", "help": "All IOs will be set to isolate(floating) state by default during sleep.\nSince the power supply of PSRAM is not lost during lightsleep, if its CS pin is recognized as\nlow level(selected state) in the floating state, there will be a large current leakage, and the\ndata in PSRAM may be corrupted by random signals on other SPI pins.\nSelect this option will set the CS pin of PSRAM to PULL-UP state during sleep, but this will\nincrease the sleep current about 10 uA.\nIf you are developing with esp32xx modules, you must select this option, but if you are developing\nwith chips, you can also pull up the CS pin of PSRAM in the external circuit to save power\nconsumption caused by internal pull-up during sleep.\n(!!! Don't deselect this option if you don't have external PSRAM CS pin pullups.)", "id": "ESP_SLEEP_PSRAM_LEAKAGE_WORKAROUND", "name": "ESP_SLEEP_PSRAM_LEAKAGE_WORKAROUND", "range": null, "title": "Pull-up PSRAM CS pin in light sleep", "type": "bool"}, {"children": [], "depends_on": "!ESP_SLEEP_POWER_DOWN_FLASH && (ESP_SLEEP_PSRAM_LEAKAGE_WORKAROUND || ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND)", "help": "To reduce leakage current, some types of SPI Flash/RAM only need to pull up the CS pin\nduring light sleep. But there are also some kinds of SPI Flash/RAM that need to pull up\nall pins. It depends on the SPI Flash/RAM chip used.", "id": "ESP_SLEEP_MSPI_NEED_ALL_IO_PU", "name": "ESP_SLEEP_MSPI_NEED_ALL_IO_PU", "range": null, "title": "Pull-up all SPI pins in light sleep", "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_SLEEP_RTC_BUS_ISO_WORKAROUND", "name": "ESP_SLEEP_RTC_BUS_ISO_WORKAROUND", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": "esp32c2, esp32c3, esp32s3, esp32c6 and esp32h2 will reset at wake-up if GPIO is received\na small electrostatic pulse during light sleep, with specific condition\n\n- GPIO needs to be configured as input-mode only\n- The pin receives a small electrostatic pulse, and reset occurs when the pulse\n  voltage is higher than 6 V\n\nFor GPIO set to input mode only, it is not a good practice to leave it open/floating,\nThe hardware design needs to controlled it with determined supply or ground voltage\nis necessary.\n\nThis option provides a software workaround for this issue. Configure to isolate all\nGPIO pins in sleep state.", "id": "ESP_SLEEP_GPIO_RESET_WORKAROUND", "name": "ESP_SLEEP_GPIO_RESET_WORKAROUND", "range": null, "title": "light sleep GPIO reset workaround", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32 || IDF_TARGET_ESP32S3", "help": "When the chip exits deep sleep, the CPU and the flash chip are powered on\nat the same time. CPU will run deep sleep stub first, and then\nproceed to load code from flash. Some flash chips need sufficient\ntime to pass between power on and first read operation. By default,\nwithout any extra delay, this time is approximately 900us, although\nsome flash chip types need more than that.\n\nBy default extra delay is set to 2000us. When optimizing startup time\nfor applications which require it, this value may be reduced.\n\nIf you are seeing \"flash read err, 1000\" message printed to the\nconsole after deep sleep reset, try increasing this value.", "id": "ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY", "name": "ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY", "range": null, "title": "Extra delay in deep sleep wake stub (in us)", "type": "int"}, {"children": [], "depends_on": null, "help": "When using rtc gpio wakeup source during deepsleep without external pull-up/downs, you may want to\nmake use of the internal ones.", "id": "ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS", "name": "ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS", "range": null, "title": "Allow to enable internal pull-up/downs for the Deep-Sleep wakeup IOs", "type": "bool"}], "depends_on": null, "id": "component-config-hardware-settings-sleep-config", "title": "Sleep Config", "type": "menu"}, {"children": [{"children": [], "depends_on": "IDF_TARGET_ESP32C3", "help": "Its not able to stall ESP32C3 systimer in sleep.\nTo fix related RTOS TICK issue, select it to disable related systimer during sleep.\nTODO: IDF-7036", "id": "ESP_SLEEP_SYSTIMER_STALL_WORKAROUND", "name": "ESP_SLEEP_SYSTIMER_STALL_WORKAROUND", "range": null, "title": "ESP32C3 SYSTIMER Stall Issue Workaround", "type": "bool"}], "depends_on": null, "id": "component-config-hardware-settings-esp_sleep_workaround", "title": "ESP_SLEEP_WORKAROUND", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice RTC_CLK_SRC>", "help": null, "id": "RTC_CLK_SRC_INT_RC", "name": "RTC_CLK_SRC_INT_RC", "range": null, "title": "Internal 136kHz RC oscillator", "type": "bool"}, {"children": [], "depends_on": "<choice RTC_CLK_SRC>", "help": null, "id": "RTC_CLK_SRC_EXT_CRYS", "name": "RTC_CLK_SRC_EXT_CRYS", "range": null, "title": "External 32kHz crystal", "type": "bool"}, {"children": [], "depends_on": "<choice RTC_CLK_SRC>", "help": null, "id": "RTC_CLK_SRC_EXT_OSC", "name": "RTC_CLK_SRC_EXT_OSC", "range": null, "title": "External 32kHz oscillator at 32K_XP pin", "type": "bool"}, {"children": [], "depends_on": "<choice RTC_CLK_SRC>", "help": null, "id": "RTC_CLK_SRC_INT_RC32K", "name": "RTC_CLK_SRC_INT_RC32K", "range": null, "title": "Internal 32kHz RC oscillator", "type": "bool"}], "depends_on": null, "help": "Choose which clock is used as RTC clock source.", "id": "component-config-hardware-settings-rtc-clock-config-rtc-clock-source", "name": "RTC_CLK_SRC", "title": "RTC clock source", "type": "choice"}, {"children": [], "depends_on": null, "help": "When the startup code initializes RTC_SLOW_CLK, it can perform\ncalibration by comparing the RTC_SLOW_CLK frequency with main XTAL\nfrequency. This option sets the number of RTC_SLOW_CLK cycles measured\nby the calibration routine. Higher numbers increase calibration\nprecision, which may be important for applications which spend a lot of\ntime in deep sleep. Lower numbers reduce startup time.\n\nWhen this option is set to 0, clock calibration will not be performed at\nstartup, and approximate clock frequencies will be assumed:\n\n- 136000 Hz if internal RC oscillator is used as clock source. For this use value 1024.\n- 32768 Hz if the 32k crystal oscillator is used. For this use value 3000 or more.\n    In case more value will help improve the definition of the launch of the crystal.\n    If the crystal could not start, it will be switched to internal RC.", "id": "RTC_CLK_CAL_CYCLES", "name": "RTC_CLK_CAL_CYCLES", "range": [0, 32766], "title": "Number of cycles for RTC_SLOW_CLK calibration", "type": "int"}], "depends_on": null, "id": "component-config-hardware-settings-rtc-clock-config", "title": "RTC Clock Config", "type": "menu"}, {"children": [{"children": [], "depends_on": null, "help": "Place peripheral control functions (e.g. periph_module_reset) into IRAM,\nso that these functions can be IRAM-safe and able to be called in the other IRAM interrupt context.", "id": "PERIPH_CTRL_FUNC_IN_IRAM", "name": "PERIPH_CTRL_FUNC_IN_IRAM", "range": null, "title": "Place peripheral control functions into IRAM", "type": "bool"}], "depends_on": null, "id": "component-config-hardware-settings-peripheral-control", "title": "Peripheral Control", "type": "menu"}, {"children": [{"children": [], "depends_on": "SOC_ETM_SUPPORTED", "help": "Wether to enable the debug log message for ETM core driver.\nNote that, this option only controls the ETM related driver log, won't affect other drivers.", "id": "ETM_ENABLE_DEBUG_LOG", "name": "ETM_ENABLE_DEBUG_LOG", "range": null, "title": "Enable debug log", "type": "bool"}], "depends_on": "SOC_ETM_SUPPORTED", "id": "component-config-hardware-settings-etm-configuration", "title": "ETM Configuration", "type": "menu"}, {"children": [{"children": [], "depends_on": "SOC_GDMA_SUPPORTED", "help": "Place GDMA control functions (like start/stop/append/reset) into IRAM,\nso that these functions can be IRAM-safe and able to be called in the other IRAM interrupt context.\nEnabling this option can improve driver performance as well.", "id": "GDMA_CTRL_FUNC_IN_IRAM", "name": "GDMA_CTRL_FUNC_IN_IRAM", "range": null, "title": "Place GDMA control functions into IRAM", "type": "bool"}, {"children": [], "depends_on": "SOC_GDMA_SUPPORTED", "help": "This will ensure the GDMA interrupt handler is IRAM-Safe, allow to avoid flash\ncache misses, and also be able to run whilst the cache is disabled.\n(e.g. SPI Flash write).", "id": "GDMA_ISR_IRAM_SAFE", "name": "GDMA_ISR_IRAM_SAFE", "range": null, "title": "GDMA ISR IRAM-Safe", "type": "bool"}], "depends_on": "SOC_GDMA_SUPPORTED", "id": "component-config-hardware-settings-gdma-configuration", "title": "GDMA Configuration", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "SOC_XTAL_SUPPORT_24M && <choice XTAL_FREQ_SEL>", "help": null, "id": "XTAL_FREQ_24", "name": "XTAL_FREQ_24", "range": null, "title": "24 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_XTAL_SUPPORT_26M && <choice XTAL_FREQ_SEL>", "help": null, "id": "XTAL_FREQ_26", "name": "XTAL_FREQ_26", "range": null, "title": "26 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_XTAL_SUPPORT_32M && <choice XTAL_FREQ_SEL>", "help": null, "id": "XTAL_FREQ_32", "name": "XTAL_FREQ_32", "range": null, "title": "32 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_XTAL_SUPPORT_40M && <choice XTAL_FREQ_SEL>", "help": null, "id": "XTAL_FREQ_40", "name": "XTAL_FREQ_40", "range": null, "title": "40 MHz", "type": "bool"}, {"children": [], "depends_on": "SOC_XTAL_SUPPORT_AUTO_DETECT && <choice XTAL_FREQ_SEL>", "help": null, "id": "XTAL_FREQ_AUTO", "name": "XTAL_FREQ_AUTO", "range": null, "title": "Autodetect", "type": "bool"}], "depends_on": null, "help": "This option selects the operating frequency of the XTAL (crystal) clock used to drive the ESP target.\nThe selected value MUST reflect the frequency of the given hardware.\n\nNote: The XTAL_FREQ_AUTO option allows the ESP target to automatically estimating XTAL clock's\noperating frequency. However, this feature is only supported on the ESP32. The ESP32 uses the\ninternal 8MHZ as a reference when estimating. Due to the internal oscillator's frequency being\ntemperature dependent, usage of the XTAL_FREQ_AUTO is not recommended in applications that operate\nin high ambient temperatures or use high-temperature qualified chips and modules.", "id": "component-config-hardware-settings-main-xtal-config-main-xtal-frequency", "name": "XTAL_FREQ_SEL", "title": "Main XTAL frequency", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "XTAL_FREQ", "name": "XTAL_FREQ", "range": null, "title": null, "type": "int"}], "depends_on": null, "id": "component-config-hardware-settings-main-xtal-config", "title": "Main XTAL Config", "type": "menu"}, {"children": [{"children": [{"children": [{"children": [], "depends_on": "<choice ESP_CRYPTO_DPA_PROTECTION_LEVEL>", "help": null, "id": "ESP_CRYPTO_DPA_PROTECTION_LEVEL_LOW", "name": "ESP_CRYPTO_DPA_PROTECTION_LEVEL_LOW", "range": null, "title": "Security level low", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_CRYPTO_DPA_PROTECTION_LEVEL>", "help": null, "id": "ESP_CRYPTO_DPA_PROTECTION_LEVEL_MEDIUM", "name": "ESP_CRYPTO_DPA_PROTECTION_LEVEL_MEDIUM", "range": null, "title": "Security level medium", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_CRYPTO_DPA_PROTECTION_LEVEL>", "help": null, "id": "ESP_CRYPTO_DPA_PROTECTION_LEVEL_HIGH", "name": "ESP_CRYPTO_DPA_PROTECTION_LEVEL_HIGH", "range": null, "title": "Security level high", "type": "bool"}], "depends_on": "ESP_CRYPTO_DPA_PROTECTION_AT_STARTUP && SOC_CRYPTO_DPA_PROTECTION_SUPPORTED", "help": "Configure the DPA protection security level", "id": "component-config-hardware-settings-crypto-dpa-protection-enable-crypto-dpa-protection-at-startup-dpa-protection-level", "name": "ESP_CRYPTO_DPA_PROTECTION_LEVEL", "title": "DPA protection level", "type": "choice"}], "depends_on": "SOC_CRYPTO_DPA_PROTECTION_SUPPORTED", "help": "This config controls the DPA (Differential Power Analysis) protection\nknob for the crypto peripherals. DPA protection dynamically adjusts the\nclock frequency of the crypto peripheral. DPA protection helps to make it\ndifficult to perform SCA attacks on the crypto peripherals. However,\nthere is also associated performance impact based on the security level\nset. Please refer to the TRM for more details.", "id": "ESP_CRYPTO_DPA_PROTECTION_AT_STARTUP", "name": "ESP_CRYPTO_DPA_PROTECTION_AT_STARTUP", "range": null, "title": "Enable crypto DPA protection at startup", "type": "bool"}, {"children": [], "depends_on": "SOC_CRYPTO_DPA_PROTECTION_SUPPORTED", "help": null, "id": "ESP_CRYPTO_DPA_PROTECTION_LEVEL", "name": "ESP_CRYPTO_DPA_PROTECTION_LEVEL", "range": null, "title": null, "type": "int"}], "depends_on": "SOC_CRYPTO_DPA_PROTECTION_SUPPORTED", "id": "component-config-hardware-settings-crypto-dpa-protection", "title": "Crypto DPA Protection", "type": "menu"}], "depends_on": null, "id": "component-config-hardware-settings", "title": "Hardware Settings", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "IDF_ENV_FPGA && <choice ESP_DEFAULT_CPU_FREQ_MHZ>", "help": null, "id": "ESP_DEFAULT_CPU_FREQ_MHZ_40", "name": "ESP_DEFAULT_CPU_FREQ_MHZ_40", "range": null, "title": "40 MHz", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_DEFAULT_CPU_FREQ_MHZ>", "help": null, "id": "ESP_DEFAULT_CPU_FREQ_MHZ_80", "name": "ESP_DEFAULT_CPU_FREQ_MHZ_80", "range": null, "title": "80 MHz", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_DEFAULT_CPU_FREQ_MHZ>", "help": null, "id": "ESP_DEFAULT_CPU_FREQ_MHZ_120", "name": "ESP_DEFAULT_CPU_FREQ_MHZ_120", "range": null, "title": "120 MHz", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_DEFAULT_CPU_FREQ_MHZ>", "help": null, "id": "ESP_DEFAULT_CPU_FREQ_MHZ_160", "name": "ESP_DEFAULT_CPU_FREQ_MHZ_160", "range": null, "title": "160 MHz", "type": "bool"}], "depends_on": null, "help": "CPU frequency to be set on application startup.", "id": "component-config-esp-system-settings-cpu-frequency", "name": "ESP_DEFAULT_CPU_FREQ_MHZ", "title": "CPU frequency", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_DEFAULT_CPU_FREQ_MHZ", "name": "ESP_DEFAULT_CPU_FREQ_MHZ", "range": null, "title": null, "type": "int"}, {"children": [{"children": [], "depends_on": "<choice ESP_SYSTEM_PANIC>", "help": "Outputs the relevant registers over the serial port and halt the\nprocessor. Needs a manual reset to restart.", "id": "ESP_SYSTEM_PANIC_PRINT_HALT", "name": "ESP_SYSTEM_PANIC_PRINT_HALT", "range": null, "title": "Print registers and halt", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_SYSTEM_PANIC>", "help": "Outputs the relevant registers over the serial port and immediately\nreset the processor.", "id": "ESP_SYSTEM_PANIC_PRINT_REBOOT", "name": "ESP_SYSTEM_PANIC_PRINT_REBOOT", "range": null, "title": "Print registers and reboot", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_SYSTEM_PANIC>", "help": "Just resets the processor without outputting anything", "id": "ESP_SYSTEM_PANIC_SILENT_REBOOT", "name": "ESP_SYSTEM_PANIC_SILENT_REBOOT", "range": null, "title": "Silent reboot", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_SYSTEM_PANIC>", "help": "Invoke gdbstub on the serial port, allowing for gdb to attach to it to do a postmortem\nof the crash.", "id": "ESP_SYSTEM_PANIC_GDBSTUB", "name": "ESP_SYSTEM_PANIC_GDBSTUB", "range": null, "title": "GDBStub on panic", "type": "bool"}, {"children": [], "depends_on": "!IDF_TARGET_ESP32C2 && <choice ESP_SYSTEM_PANIC>", "help": "Invoke gdbstub on the serial port, allowing for gdb to attach to it and to do a debug on runtime.", "id": "ESP_SYSTEM_GDBSTUB_RUNTIME", "name": "ESP_SYSTEM_GDBSTUB_RUNTIME", "range": null, "title": "GDBStub at runtime", "type": "bool"}], "depends_on": null, "help": "If FreeRTOS detects unexpected behaviour or an unhandled exception, the panic handler is\ninvoked. Configure the panic handler's action here.", "id": "component-config-esp-system-settings-panic-handler-behaviour", "name": "ESP_SYSTEM_PANIC", "title": "Panic handler behaviour", "type": "choice"}, {"children": [], "depends_on": "ESP_SYSTEM_PANIC_PRINT_REBOOT", "help": "After the panic handler executes, you can specify a number of seconds to\nwait before the device reboots.", "id": "ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS", "name": "ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS", "range": [0, 99], "title": "Panic reboot delay (Seconds)", "type": "int"}, {"children": [], "depends_on": null, "help": "Only initialize and use the main core.", "id": "ESP_SYSTEM_SINGLE_CORE_MODE", "name": "ESP_SYSTEM_SINGLE_CORE_MODE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_SYSTEM_RTC_EXT_XTAL", "name": "ESP_SYSTEM_RTC_EXT_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_SYSTEM_RTC_EXT_OSC", "name": "ESP_SYSTEM_RTC_EXT_OSC", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "ESP_SYSTEM_RTC_EXT_XTAL", "help": "To reduce the startup time of an external RTC crystal,\nwe bootstrap it with a 32kHz square wave for a fixed number of cycles.\nSetting 0 will disable bootstrapping (if disabled, the crystal may take\nlonger to start up or fail to oscillate under some conditions).\n\nIf this value is too high, a faulty crystal may initially start and then fail.\nIf this value is too low, an otherwise good crystal may not start.\n\nTo accurately determine if the crystal has started,\nset a larger \"Number of cycles for RTC_SLOW_CLK calibration\" (about 3000).", "id": "ESP_SYSTEM_RTC_EXT_XTAL_BOOTSTRAP_CYCLES", "name": "ESP_SYSTEM_RTC_EXT_XTAL_BOOTSTRAP_CYCLES", "range": null, "title": "Bootstrap cycles for external 32kHz crystal", "type": "int"}, {"children": [], "depends_on": "SOC_RTC_FAST_MEM_SUPPORTED", "help": null, "id": "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK", "name": "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK", "help": "This config option allows to add RTC fast memory region to system heap with capability\nsimilar to that of DRAM region but without DMA. This memory will be consumed first per\nheap initialization order by early startup services and scheduler related code. Speed\nwise RTC fast memory operates on APB clock and hence does not have much performance impact.", "id": "ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP", "name": "ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP", "range": null, "title": "Enable RTC fast memory for dynamic allocations", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ARCH_RISCV", "help": "Generate DWARF information for each function of the project. These information will parsed and used to\nperform backtracing when panics occur. Activating this option will activate asynchronous frame unwinding\nand generation of both .eh_frame and .eh_frame_hdr sections, resulting in a bigger binary size (20% to\n100% larger). The main purpose of this option is to be able to have a backtrace parsed and printed by\nthe program itself, regardless of the serial monitor used.\nThis option shall NOT be used for production.", "id": "ESP_SYSTEM_USE_EH_FRAME", "name": "ESP_SYSTEM_USE_EH_FRAME", "range": null, "title": "Generate and use eh_frame for backtracing", "type": "bool"}, {"children": [{"children": [], "depends_on": "SOC_CPU_IDRAM_SPLIT_USING_PMP", "help": "If enabled, the CPU watches all the memory access and raises an exception in case\nof any memory violation. This feature automatically splits\nthe SRAM memory, using PMP, into data and instruction segments and sets Read/Execute permissions\nfor the instruction part (below given splitting address) and Read/Write permissions\nfor the data part (above the splitting address). The memory protection is effective\non all access through the IRAM0 and DRAM0 buses.", "id": "ESP_SYSTEM_PMP_IDRAM_SPLIT", "name": "ESP_SYSTEM_PMP_IDRAM_SPLIT", "range": null, "title": "Enable IRAM/DRAM split protection", "type": "bool"}, {"children": [{"children": [], "depends_on": "ESP_SYSTEM_MEMPROT_FEATURE", "help": "Once locked, memory protection settings cannot be changed anymore.\nThe lock is reset only on the chip startup.", "id": "ESP_SYSTEM_MEMPROT_FEATURE_LOCK", "name": "ESP_SYSTEM_MEMPROT_FEATURE_LOCK", "range": null, "title": "Lock memory protection settings", "type": "bool"}], "depends_on": "SOC_MEMPROT_SUPPORTED", "help": "If enabled, the permission control module watches all the memory access and fires the panic handler\nif a permission violation is detected. This feature automatically splits\nthe SRAM memory into data and instruction segments and sets Read/Execute permissions\nfor the instruction part (below given splitting address) and Read/Write permissions\nfor the data part (above the splitting address). The memory protection is effective\non all access through the IRAM0 and DRAM0 buses.", "id": "ESP_SYSTEM_MEMPROT_FEATURE", "name": "ESP_SYSTEM_MEMPROT_FEATURE", "range": null, "title": "Enable memory protection", "type": "bool"}], "depends_on": null, "id": "component-config-esp-system-settings-memory-protection", "title": "Memory protection", "type": "menu"}, {"children": [], "depends_on": null, "help": "Config system event queue size in different application.", "id": "ESP_SYSTEM_EVENT_QUEUE_SIZE", "name": "ESP_SYSTEM_EVENT_QUEUE_SIZE", "range": null, "title": "System event queue size", "type": "int"}, {"children": [], "depends_on": null, "help": "Config system event task stack size in different application.", "id": "ESP_SYSTEM_EVENT_TASK_STACK_SIZE", "name": "ESP_SYSTEM_EVENT_TASK_STACK_SIZE", "range": null, "title": "Event loop task stack size", "type": "int"}, {"children": [], "depends_on": null, "help": "Configure the \"main task\" stack size. This is the stack of the task\nwhich calls app_main(). If app_main() returns then this task is deleted\nand its stack memory is freed.", "id": "ESP_MAIN_TASK_STACK_SIZE", "name": "ESP_MAIN_TASK_STACK_SIZE", "range": null, "title": "Main task stack size", "type": "int"}, {"children": [{"children": [], "depends_on": "<choice ESP_MAIN_TASK_AFFINITY>", "help": null, "id": "ESP_MAIN_TASK_AFFINITY_CPU0", "name": "ESP_MAIN_TASK_AFFINITY_CPU0", "range": null, "title": "CPU0", "type": "bool"}, {"children": [], "depends_on": "!FREERTOS_UNICORE && <choice ESP_MAIN_TASK_AFFINITY>", "help": null, "id": "ESP_MAIN_TASK_AFFINITY_CPU1", "name": "ESP_MAIN_TASK_AFFINITY_CPU1", "range": null, "title": "CPU1", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_MAIN_TASK_AFFINITY>", "help": null, "id": "ESP_MAIN_TASK_AFFINITY_NO_AFFINITY", "name": "ESP_MAIN_TASK_AFFINITY_NO_AFFINITY", "range": null, "title": "No affinity", "type": "bool"}], "depends_on": null, "help": "Configure the \"main task\" core affinity. This is the used core of the task\nwhich calls app_main(). If app_main() returns then this task is deleted.", "id": "component-config-esp-system-settings-main-task-core-affinity", "name": "ESP_MAIN_TASK_AFFINITY", "title": "Main task core affinity", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAIN_TASK_AFFINITY", "name": "ESP_MAIN_TASK_AFFINITY", "range": null, "title": null, "type": "hex"}, {"children": [], "depends_on": null, "help": "Minimal value of size, in bytes, accepted to execute a expression\nwith shared stack.", "id": "ESP_MINIMAL_SHARED_STACK_SIZE", "name": "ESP_MINIMAL_SHARED_STACK_SIZE", "range": null, "title": "Minimal allowed size for shared stack", "type": "int"}, {"children": [{"children": [], "depends_on": "<choice ESP_CONSOLE_UART>", "help": null, "id": "ESP_CONSOLE_UART_DEFAULT", "name": "ESP_CONSOLE_UART_DEFAULT", "range": null, "title": "Default: UART0", "type": "bool"}, {"children": [], "depends_on": "(IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3) && !TINY_USB && <choice ESP_CONSOLE_UART>", "help": null, "id": "ESP_CONSOLE_USB_CDC", "name": "ESP_CONSOLE_USB_CDC", "range": null, "title": "USB CDC", "type": "bool"}, {"children": [], "depends_on": "SOC_USB_SERIAL_JTAG_SUPPORTED && <choice ESP_CONSOLE_UART>", "help": null, "id": "ESP_CONSOLE_USB_SERIAL_JTAG", "name": "ESP_CONSOLE_USB_SERIAL_JTAG", "range": null, "title": "USB Serial/JTAG Controller", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_CONSOLE_UART>", "help": null, "id": "ESP_CONSOLE_UART_CUSTOM", "name": "ESP_CONSOLE_UART_CUSTOM", "range": null, "title": "Custom UART", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_CONSOLE_UART>", "help": null, "id": "ESP_CONSOLE_NONE", "name": "ESP_CONSOLE_NONE", "range": null, "title": "None", "type": "bool"}], "depends_on": null, "help": "Select where to send console output (through stdout and stderr).\n\n- Default is to use UART0 on pre-defined GPIOs.\n- If \"Custom\" is selected, UART0 or UART1 can be chosen,\n  and any pins can be selected.\n- If \"None\" is selected, there will be no console output on any UART, except\n  for initial output from ROM bootloader. This ROM output can be suppressed by\n  GPIO strapping or EFUSE, refer to chip datasheet for details.\n- On chips with USB OTG peripheral, \"USB CDC\" option redirects output to the\n  CDC port. This option uses the CDC driver in the chip ROM.\n  This option is incompatible with TinyUSB stack.\n- On chips with an USB serial/JTAG debug controller, selecting the option\n  for that redirects output to the CDC/ACM (serial port emulation) component\n  of that device.", "id": "component-config-esp-system-settings-channel-for-console-output", "name": "ESP_CONSOLE_UART", "title": "Channel for console output", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice ESP_CONSOLE_SECONDARY>", "help": null, "id": "ESP_CONSOLE_SECONDARY_NONE", "name": "ESP_CONSOLE_SECONDARY_NONE", "range": null, "title": "No secondary console", "type": "bool"}, {"children": [], "depends_on": "!ESP_CONSOLE_USB_SERIAL_JTAG && <choice ESP_CONSOLE_SECONDARY>", "help": "This option supports output through USB_SERIAL_JTAG port when the UART0 port is not connected.\nThe output currently only supports non-blocking mode without using the console.\nIf you want to output in blocking mode with REPL or input through USB_SERIAL_JTAG port,\nplease change the primary config to ESP_CONSOLE_USB_SERIAL_JTAG above.", "id": "ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG", "name": "ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG", "range": null, "title": "USB_SERIAL_JTAG PORT", "type": "bool"}], "depends_on": "SOC_USB_SERIAL_JTAG_SUPPORTED", "help": "This secondary option supports output through other specific port like USB_SERIAL_JTAG\nwhen UART0 port as a primary is selected but not connected. This secondary output currently only supports\nnon-blocking mode without using REPL. If you want to output in blocking mode with REPL or\ninput through this secondary port, please change the primary config to this port\nin `Channel for console output` menu.", "id": "component-config-esp-system-settings-channel-for-console-secondary-output", "name": "ESP_CONSOLE_SECONDARY", "title": "Channel for console secondary output", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED", "name": "ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_CONSOLE_UART", "name": "ESP_CONSOLE_UART", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_CONSOLE_MULTIPLE_UART", "name": "ESP_CONSOLE_MULTIPLE_UART", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice ESP_CONSOLE_UART_NUM>", "help": null, "id": "ESP_CONSOLE_UART_CUSTOM_NUM_0", "name": "ESP_CONSOLE_UART_CUSTOM_NUM_0", "range": null, "title": "UART0", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_CONSOLE_UART_NUM>", "help": null, "id": "ESP_CONSOLE_UART_CUSTOM_NUM_1", "name": "ESP_CONSOLE_UART_CUSTOM_NUM_1", "range": null, "title": "UART1", "type": "bool"}], "depends_on": "ESP_CONSOLE_UART_CUSTOM && ESP_CONSOLE_MULTIPLE_UART", "help": "This UART peripheral is used for console output from the ESP-IDF Bootloader and the app.\n\nIf the configuration is different in the Bootloader binary compared to the app binary, UART\nis reconfigured after the bootloader exits and the app starts.\n\nDue to an ESP32 ROM bug, UART2 is not supported for console output\nvia esp_rom_printf.", "id": "component-config-esp-system-settings-uart-peripheral-to-use-for-console-output-0-1-", "name": "ESP_CONSOLE_UART_NUM", "title": "UART peripheral to use for console output (0-1)", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_CONSOLE_UART_NUM", "name": "ESP_CONSOLE_UART_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": "ESP_CONSOLE_UART_CUSTOM", "help": "This GPIO is used for console UART TX output in the ESP-IDF Bootloader and the app (including\nboot log output and default standard output and standard error of the app).\n\nIf the configuration is different in the Bootloader binary compared to the app binary, UART\nis reconfigured after the bootloader exits and the app starts.", "id": "ESP_CONSOLE_UART_TX_GPIO", "name": "ESP_CONSOLE_UART_TX_GPIO", "range": null, "title": "UART TX on GPIO#", "type": "int"}, {"children": [], "depends_on": "ESP_CONSOLE_UART_CUSTOM", "help": "This GPIO is used for UART RX input in the ESP-IDF Bootloader and the app (including\ndefault default standard input of the app).\n\nNote: The default ESP-IDF Bootloader configures this pin but doesn't read anything from the UART.\n\nIf the configuration is different in the Bootloader binary compared to the app binary, UART\nis reconfigured after the bootloader exits and the app starts.", "id": "ESP_CONSOLE_UART_RX_GPIO", "name": "ESP_CONSOLE_UART_RX_GPIO", "range": null, "title": "UART RX on GPIO#", "type": "int"}, {"children": [], "depends_on": "ESP_CONSOLE_UART", "help": "This baud rate is used by both the ESP-IDF Bootloader and the app (including\nboot log output and default standard input/output/error of the app).\n\nThe app's maximum baud rate depends on the UART clock source. If Power Management is disabled,\nthe UART clock source is the APB clock and all baud rates in the available range will be sufficiently\naccurate. If Power Management is enabled, REF_TICK clock source is used so the baud rate is divided\nfrom 1MHz. Baud rates above 1Mbps are not possible and values between 500Kbps and 1Mbps may not be\naccurate.\n\nIf the configuration is different in the Bootloader binary compared to the app binary, UART\nis reconfigured after the bootloader exits and the app starts.", "id": "ESP_CONSOLE_UART_BAUDRATE", "name": "ESP_CONSOLE_UART_BAUDRATE", "range": [1200, 4000000], "title": "UART console baud rate", "type": "int"}, {"children": [], "depends_on": "ESP_CONSOLE_USB_CDC", "help": "Set the size of USB CDC RX buffer. Increase the buffer size if your application\nis often receiving data over USB CDC.", "id": "ESP_CONSOLE_USB_CDC_RX_BUF_SIZE", "name": "ESP_CONSOLE_USB_CDC_RX_BUF_SIZE", "range": null, "title": "Size of USB CDC RX buffer", "type": "int"}, {"children": [], "depends_on": "ESP_CONSOLE_USB_CDC", "help": "If enabled, esp_rom_printf and ESP_EARLY_LOG output will also be sent over USB CDC.\nDisabling this option saves about 1kB or RAM.", "id": "ESP_CONSOLE_USB_CDC_SUPPORT_ETS_PRINTF", "name": "ESP_CONSOLE_USB_CDC_SUPPORT_ETS_PRINTF", "range": null, "title": "Enable esp_rom_printf / ESP_EARLY_LOG via USB CDC", "type": "bool"}, {"children": [{"children": [], "depends_on": "ESP_INT_WDT", "help": "The timeout of the watchdog, in miliseconds. Make this higher than the FreeRTOS tick rate.", "id": "ESP_INT_WDT_TIMEOUT_MS", "name": "ESP_INT_WDT_TIMEOUT_MS", "range": [10, 10000], "title": "Interrupt watchdog timeout (ms)", "type": "int"}, {"children": [], "depends_on": "ESP_INT_WDT && !FREERTOS_UNICORE", "help": "Also detect if interrupts on CPU 1 are disabled for too long.", "id": "ESP_INT_WDT_CHECK_CPU1", "name": "ESP_INT_WDT_CHECK_CPU1", "range": null, "title": "Also watch CPU1 tick interrupt", "type": "bool"}], "depends_on": null, "help": "This watchdog timer can detect if the FreeRTOS tick interrupt has not been called for a certain time,\neither because a task turned off interrupts and did not turn them on for a long time, or because an\ninterrupt handler did not return. It will try to invoke the panic handler first and failing that\nreset the SoC.", "id": "ESP_INT_WDT", "name": "ESP_INT_WDT", "range": null, "title": "Interrupt watchdog", "type": "bool"}, {"children": [{"children": [], "depends_on": "ESP_TASK_WDT_EN", "help": null, "id": "ESP_TASK_WDT_USE_ESP_TIMER", "name": "ESP_TASK_WDT_USE_ESP_TIMER", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [], "depends_on": "ESP_TASK_WDT_INIT", "help": "If this option is enabled, the Task Watchdog Timer will be configured to\ntrigger the panic handler when it times out. This can also be configured\nat run time (see Task Watchdog Timer API Reference)", "id": "ESP_TASK_WDT_PANIC", "name": "ESP_TASK_WDT_PANIC", "range": null, "title": "Invoke panic handler on Task Watchdog timeout", "type": "bool"}, {"children": [], "depends_on": "ESP_TASK_WDT_INIT", "help": "Timeout period configuration for the Task Watchdog Timer in seconds.\nThis is also configurable at run time (see Task Watchdog Timer API Reference)", "id": "ESP_TASK_WDT_TIMEOUT_S", "name": "ESP_TASK_WDT_TIMEOUT_S", "range": [1, 60], "title": "Task Watchdog timeout period (seconds)", "type": "int"}, {"children": [], "depends_on": "ESP_TASK_WDT_INIT", "help": "If this option is enabled, the Task Watchdog Timer will watch the CPU0\nIdle Task. Having the Task Watchdog watch the Idle Task allows for detection\nof CPU starvation as the Idle Task not being called is usually a symptom of\nCPU starvation. Starvation of the Idle Task is detrimental as FreeRTOS household\ntasks depend on the Idle Task getting some runtime every now and then.", "id": "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0", "name": "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0", "range": null, "title": "Watch CPU0 Idle Task", "type": "bool"}, {"children": [], "depends_on": "ESP_TASK_WDT_INIT && !FREERTOS_UNICORE", "help": "If this option is enabled, the Task Watchdog Timer will wach the CPU1\nIdle Task.", "id": "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1", "name": "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1", "range": null, "title": "Watch CPU1 Idle Task", "type": "bool"}], "depends_on": "ESP_TASK_WDT_EN", "help": "Enabling this option will cause the Task Watchdog Timer to be initialized\nautomatically at startup.", "id": "ESP_TASK_WDT_INIT", "name": "ESP_TASK_WDT_INIT", "range": null, "title": "Initialize Task Watchdog Timer on startup", "type": "bool"}], "depends_on": null, "help": "The Task Watchdog Timer can be used to make sure individual tasks are still\nrunning. Enabling this option will enable the Task Watchdog Timer. It can be\neither initialized automatically at startup or initialized after startup\n(see Task Watchdog Timer API Reference)", "id": "ESP_TASK_WDT_EN", "name": "ESP_TASK_WDT_EN", "range": null, "title": "Enable Task Watchdog Timer", "type": "bool"}, {"children": [{"children": [], "depends_on": "ESP_XT_WDT", "help": "Timeout period configuration for the XTAL32K watchdog timer based on RTC_CLK.", "id": "ESP_XT_WDT_TIMEOUT", "name": "ESP_XT_WDT_TIMEOUT", "range": null, "title": "XTAL32K watchdog timeout period", "type": "int"}, {"children": [], "depends_on": "ESP_XT_WDT", "help": "Enable this to automatically switch to BACKUP32K_CLK as the source of RTC_SLOW_CLK when\nthe watchdog timer expires.", "id": "ESP_XT_WDT_BACKUP_CLK_ENABLE", "name": "ESP_XT_WDT_BACKUP_CLK_ENABLE", "range": null, "title": "Automatically switch to BACKUP32K_CLK when timer expires", "type": "bool"}], "depends_on": "!IDF_TARGET_ESP32 && (ESP_SYSTEM_RTC_EXT_OSC || ESP_SYSTEM_RTC_EXT_XTAL)", "help": "This watchdog timer can detect oscillation failure of the XTAL32K_CLK. When such a failure\nis detected the hardware can be set up to automatically switch to BACKUP32K_CLK and generate\nan interrupt.", "id": "ESP_XT_WDT", "name": "ESP_XT_WDT", "range": null, "title": "Initialize XTAL32K watchdog timer on startup", "type": "bool"}, {"children": [], "depends_on": null, "help": "If this option is disabled (default), the panic handler code is placed in flash not IRAM.\nThis means that if ESP-IDF crashes while flash cache is disabled, the panic handler will\nautomatically re-enable flash cache before running GDB Stub or Core Dump. This adds some minor\nrisk, if the flash cache status is also corrupted during the crash.\n\nIf this option is enabled, the panic handler code (including required UART functions) is placed\nin IRAM. This may be necessary to debug some complex issues with crashes while flash cache is\ndisabled (for example, when writing to SPI flash) or when flash cache is corrupted when an exception\nis triggered.", "id": "ESP_PANIC_HANDLER_IRAM", "name": "ESP_PANIC_HANDLER_IRAM", "range": null, "title": "Place panic handler code in IRAM", "type": "bool"}, {"children": [], "depends_on": "!ESP32_TRAX && !ESP32S2_TRAX && !ESP32S3_TRAX", "help": "Debug stubs are used by OpenOCD to execute pre-compiled onboard code\nwhich does some useful debugging stuff, e.g. GCOV data dump.", "id": "ESP_DEBUG_STUBS_ENABLE", "name": "ESP_DEBUG_STUBS_ENABLE", "range": null, "title": "OpenOCD debug stubs", "type": "bool"}, {"children": [], "depends_on": null, "help": "The FreeRTOS panic and unhandled exception handers can detect a JTAG OCD debugger and\ninstead of panicking, have the debugger stop on the offending instruction.", "id": "ESP_DEBUG_OCDAWARE", "name": "ESP_DEBUG_OCDAWARE", "range": null, "title": "Make exception and panic handlers JTAG/OCD aware", "type": "bool"}, {"children": [{"children": [], "depends_on": "IDF_TARGET_ESP32 && <choice ESP_SYSTEM_CHECK_INT_LEVEL>", "help": "Using level 5 interrupt for Interrupt Watchdog and other system checks.", "id": "ESP_SYSTEM_CHECK_INT_LEVEL_5", "name": "ESP_SYSTEM_CHECK_INT_LEVEL_5", "range": null, "title": "Level 5 interrupt", "type": "bool"}, {"children": [], "depends_on": "!BTDM_CTRL_HLI && <choice ESP_SYSTEM_CHECK_INT_LEVEL>", "help": "Using level 4 interrupt for Interrupt Watchdog and other system checks.", "id": "ESP_SYSTEM_CHECK_INT_LEVEL_4", "name": "ESP_SYSTEM_CHECK_INT_LEVEL_4", "range": null, "title": "Level 4 interrupt", "type": "bool"}], "depends_on": null, "help": "Interrupt level to use for Interrupt Watchdog and other system checks.", "id": "component-config-esp-system-settings-interrupt-level-to-use-for-interrupt-watchdog-and-other-system-checks", "name": "ESP_SYSTEM_CHECK_INT_LEVEL", "title": "Interrupt level to use for Interrupt Watchdog and other system checks", "type": "choice"}, {"children": [{"children": [{"children": [{"children": [], "depends_on": "<choice ESP_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP_BROWNOUT_DET_LVL_SEL_7", "name": "ESP_BROWNOUT_DET_LVL_SEL_7", "range": null, "title": "2.51V", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP_BROWNOUT_DET_LVL_SEL_6", "name": "ESP_BROWNOUT_DET_LVL_SEL_6", "range": null, "title": "2.64V", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP_BROWNOUT_DET_LVL_SEL_5", "name": "ESP_BROWNOUT_DET_LVL_SEL_5", "range": null, "title": "2.76V", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP_BROWNOUT_DET_LVL_SEL_4", "name": "ESP_BROWNOUT_DET_LVL_SEL_4", "range": null, "title": "2.92V", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP_BROWNOUT_DET_LVL_SEL_3", "name": "ESP_BROWNOUT_DET_LVL_SEL_3", "range": null, "title": "3.10V", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP_BROWNOUT_DET_LVL_SEL_2", "name": "ESP_BROWNOUT_DET_LVL_SEL_2", "range": null, "title": "3.27V", "type": "bool"}], "depends_on": "ESP_BROWNOUT_DET", "help": "The brownout detector will reset the chip when the supply voltage is approximately\nbelow this level. Note that there may be some variation of brownout voltage level\nbetween each chip.\n\n#The voltage levels here are estimates, more work needs to be done to figure out the exact voltages\n#of the brownout threshold levels.", "id": "component-config-esp-system-settings-brownout-detector-hardware-brownout-detect-reset-brownout-voltage-level", "name": "ESP_BROWNOUT_DET_LVL_SEL", "title": "Brownout voltage level", "type": "choice"}], "depends_on": "!IDF_ENV_FPGA", "help": "The ESP32-C6 has a built-in brownout detector which can detect if the voltage is lower than\na specific value. If this happens, it will reset the chip in order to prevent unintended\nbehaviour.", "id": "ESP_BROWNOUT_DET", "name": "ESP_BROWNOUT_DET", "range": null, "title": "Hardware brownout detect & reset", "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_BROWNOUT_DET_LVL", "name": "ESP_BROWNOUT_DET_LVL", "range": null, "title": null, "type": "int"}], "depends_on": null, "id": "component-config-esp-system-settings-brownout-detector", "title": "Brownout Detector", "type": "menu"}, {"children": [], "depends_on": null, "help": "This config allows to trigger an interrupt when brownout detected. Software restart will be done\nat the end of the default callback.\nTwo occasions need to restart the chip with interrupt so far.\n(1). For ESP32 version 1, brown-out reset function doesn't work (see ESP32 errata 3.4).\n      So that we must restart from interrupt.\n(2). For special workflow, the chip needs do more things instead of restarting directly. This part\n     needs to be done in callback function of interrupt.", "id": "ESP_SYSTEM_BROWNOUT_INTR", "name": "ESP_SYSTEM_BROWNOUT_INTR", "range": null, "title": null, "type": "bool"}], "depends_on": null, "id": "component-config-esp-system-settings", "title": "ESP System Settings", "type": "menu"}, {"children": [{"children": [], "depends_on": null, "help": "Configure the IPC tasks stack size. An IPC task runs on each core (in dual core mode), and allows for\ncross-core function calls. See IPC documentation for more details. The default IPC stack size should be\nenough for most common simple use cases. However, users can increase/decrease the stack size to their\nneeds.", "id": "ESP_IPC_TASK_STACK_SIZE", "name": "ESP_IPC_TASK_STACK_SIZE", "range": [512, 65536], "title": "Inter-Processor Call (IPC) task stack size", "type": "int"}, {"children": [], "depends_on": "!FREERTOS_UNICORE", "help": "If this option is not enabled then the IPC task will keep behavior same as prior to that of ESP-IDF v4.0,\nhence IPC task will run at (configMAX_PRIORITIES - 1) priority.", "id": "ESP_IPC_USES_CALLERS_PRIORITY", "name": "ESP_IPC_USES_CALLERS_PRIORITY", "range": null, "title": "IPC runs at caller's priority", "type": "bool"}, {"children": [], "depends_on": null, "help": "The IPC ISR feature is similar to the IPC feature except that the callback function is executed in the\ncontext of a High Priority Interrupt. The IPC ISR feature is itended for low latency execution of simple\ncallbacks written in assembly on another CPU. Due to being run in a High Priority Interrupt, the assembly\ncallbacks must be written with particular restrictions (see \"IPC\" and \"High-Level Interrupt\" docs for more\ndetails).", "id": "ESP_IPC_ISR_ENABLE", "name": "ESP_IPC_ISR_ENABLE", "range": null, "title": null, "type": "bool"}], "depends_on": null, "id": "component-config-ipc-inter-processor-call-", "title": "IPC (Inter-Processor Call)", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": null, "help": "Amazon has released an SMP version of the FreeRTOS Kernel which can be found via the following link:\nhttps://github.com/FreeRTOS/FreeRTOS-Kernel/tree/smp\n\nIDF has added an experimental port of this SMP kernel located in\ncomponents/freertos/FreeRTOS-Kernel-SMP. Enabling this option will cause IDF to use the Amazon SMP\nkernel. Note that THIS FEATURE IS UNDER ACTIVE DEVELOPMENT, users use this at their own risk.\n\nLeaving this option disabled will mean the IDF FreeRTOS kernel is used instead, which is located in:\ncomponents/freertos/FreeRTOS-Kernel. Both kernel versions are SMP capable, but differ in\ntheir implementation and features.", "id": "FREERTOS_SMP", "name": "FREERTOS_SMP", "range": null, "title": "Run the Amazon SMP FreeRTOS kernel instead (FEATURE UNDER DEVELOPMENT)", "type": "bool"}, {"children": [], "depends_on": null, "help": "This version of FreeRTOS normally takes control of all cores of the CPU. Select this if you only want\nto start it on the first core. This is needed when e.g. another process needs complete control over the\nsecond core.", "id": "FREERTOS_UNICORE", "name": "FREERTOS_UNICORE", "range": null, "title": "Run FreeRTOS only on first core", "type": "bool"}, {"children": [], "depends_on": null, "help": "Sets the FreeRTOS tick interrupt frequency in Hz (see configTICK_RATE_HZ documentation for more\ndetails).", "id": "FREERTOS_HZ", "name": "FREERTOS_HZ", "range": [1, 1000], "title": "configTICK_RATE_HZ", "type": "int"}, {"children": [], "depends_on": "FREERTOS_UNICORE && !FREERTOS_SMP", "help": "Enables port specific task selection method. This option can speed up the search of ready tasks\nwhen scheduling (see configUSE_PORT_OPTIMISED_TASK_SELECTION documentation for more details).", "id": "FREERTOS_OPTIMIZED_SCHEDULER", "name": "FREERTOS_OPTIMIZED_SCHEDULER", "range": null, "title": "configUSE_PORT_OPTIMISED_TASK_SELECTION", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice FREERTOS_CHECK_STACKOVERFLOW>", "help": "Do not check for stack overflows (configCHECK_FOR_STACK_OVERFLOW = 0)", "id": "FREERTOS_CHECK_STACKOVERFLOW_NONE", "name": "FREERTOS_CHECK_STACKOVERFLOW_NONE", "range": null, "title": "No checking", "type": "bool"}, {"children": [], "depends_on": "<choice FREERTOS_CHECK_STACKOVERFLOW>", "help": "Check for stack overflows on each context switch by checking if the stack pointer is in a valid\nrange. Quick but does not detect stack overflows that happened between context switches\n(configCHECK_FOR_STACK_OVERFLOW = 1)", "id": "FREERTOS_CHECK_STACKOVERFLOW_PTRVAL", "name": "FREERTOS_CHECK_STACKOVERFLOW_PTRVAL", "range": null, "title": "Check by stack pointer value (Method 1)", "type": "bool"}, {"children": [], "depends_on": "<choice FREERTOS_CHECK_STACKOVERFLOW>", "help": "Places some magic bytes at the end of the stack area and on each context switch, check if these\nbytes are still intact. More thorough than just checking the pointer, but also slightly slower.\n(configCHECK_FOR_STACK_OVERFLOW = 2)", "id": "FREERTOS_CHECK_STACKOVERFLOW_CANARY", "name": "FREERTOS_CHECK_STACKOVERFLOW_CANARY", "range": null, "title": "Check using canary bytes (Method 2)", "type": "bool"}], "depends_on": null, "help": "Enables FreeRTOS to check for stack overflows (see configCHECK_FOR_STACK_OVERFLOW documentation for\nmore details).\n\nNote: If users do not provide their own ``vApplicationStackOverflowHook()`` function, a default\nfunction will be provided by ESP-IDF.", "id": "component-config-freertos-kernel-configcheck_for_stack_overflow", "name": "FREERTOS_CHECK_STACKOVERFLOW", "title": "configCHECK_FOR_STACK_OVERFLOW", "type": "choice"}, {"children": [], "depends_on": null, "help": "Set the number of thread local storage pointers in each task (see\nconfigNUM_THREAD_LOCAL_STORAGE_POINTERS documentation for more details).\n\nNote: In ESP-IDF, this value must be at least 1. Index 0 is reserved for use by the pthreads API\nthread-local-storage. Other indexes can be used for any desired purpose.", "id": "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS", "name": "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS", "range": [1, 256], "title": "configNUM_THREAD_LOCAL_STORAGE_POINTERS", "type": "int"}, {"children": [], "depends_on": null, "help": "Sets the idle task stack size in bytes (see configMINIMAL_STACK_SIZE documentation for more details).\n\nNote:\n\n- ESP-IDF specifies stack sizes in bytes instead of words.\n- The default size is enough for most use cases.\n- The stack size may need to be increased above the default if the app installs idle or thread local\n  storage cleanup hooks that use a lot of stack memory.\n- Conversely, the stack size can be reduced to the minimum if non of the idle features are used.", "id": "FREERTOS_IDLE_TASK_STACKSIZE", "name": "FREERTOS_IDLE_TASK_STACKSIZE", "range": [768, 32768], "title": "configMINIMAL_STACK_SIZE (Idle task stack size)", "type": "int"}, {"children": [], "depends_on": null, "help": "Enables the idle task application hook (see configUSE_IDLE_HOOK documentation for more details).\n\nNote:\n\n- The application must provide the hook function ``void vApplicationIdleHook( void );``\n- ``vApplicationIdleHook()`` is called from FreeRTOS idle task(s)\n- The FreeRTOS idle hook is NOT the same as the ESP-IDF Idle Hook, but both can be enabled\n  simultaneously.", "id": "FREERTOS_USE_IDLE_HOOK", "name": "FREERTOS_USE_IDLE_HOOK", "range": null, "title": "configUSE_IDLE_HOOK", "type": "bool"}, {"children": [], "depends_on": "FREERTOS_SMP", "help": "Enables the minimal idle task application hook (see configUSE_IDLE_HOOK documentation for more\ndetails).\n\nNote:\n\n- The application must provide the hook function ``void vApplicationMinimalIdleHook( void );``\n- ``vApplicationMinimalIdleHook()`` is called from FreeRTOS minimal idle task(s)", "id": "FREERTOS_USE_MINIMAL_IDLE_HOOK", "name": "FREERTOS_USE_MINIMAL_IDLE_HOOK", "range": null, "title": "Use FreeRTOS minimal idle hook", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enables the tick hook (see configUSE_TICK_HOOK documentation for more details).\n\nNote:\n\n- The application must provide the hook function ``void vApplicationTickHook( void );``\n- ``vApplicationTickHook()`` is called from FreeRTOS's tick handling function ``xTaskIncrementTick()``\n- The FreeRTOS tick hook is NOT the same as the ESP-IDF Tick Interrupt Hook, but both can be enabled\n  simultaneously.", "id": "FREERTOS_USE_TICK_HOOK", "name": "FREERTOS_USE_TICK_HOOK", "range": null, "title": "configUSE_TICK_HOOK", "type": "bool"}, {"children": [], "depends_on": null, "help": "Sets the maximum number of characters for task names (see configMAX_TASK_NAME_LEN documentation for\nmore details).\n\nNote: For most uses, the default of 16 characters is sufficient.", "id": "FREERTOS_MAX_TASK_NAME_LEN", "name": "FREERTOS_MAX_TASK_NAME_LEN", "range": [1, 256], "title": "configMAX_TASK_NAME_LEN", "type": "int"}, {"children": [], "depends_on": null, "help": "Enable backward compatibility with APIs prior to FreeRTOS v8.0.0. (see\nconfigENABLE_BACKWARD_COMPATIBILITY documentation for more details).", "id": "FREERTOS_ENABLE_BACKWARD_COMPATIBILITY", "name": "FREERTOS_ENABLE_BACKWARD_COMPATIBILITY", "range": null, "title": "configENABLE_BACKWARD_COMPATIBILITY", "type": "bool"}, {"children": [], "depends_on": null, "help": "Sets the timer task's priority (see configTIMER_TASK_PRIORITY documentation for more details).", "id": "FREERTOS_TIMER_TASK_PRIORITY", "name": "FREERTOS_TIMER_TASK_PRIORITY", "range": [1, 25], "title": "configTIMER_TASK_PRIORITY", "type": "int"}, {"children": [], "depends_on": null, "help": "Set the timer task's stack size (see configTIMER_TASK_STACK_DEPTH documentation for more details).", "id": "FREERTOS_TIMER_TASK_STACK_DEPTH", "name": "FREERTOS_TIMER_TASK_STACK_DEPTH", "range": [1536, 32768], "title": "configTIMER_TASK_STACK_DEPTH", "type": "int"}, {"children": [], "depends_on": null, "help": "Set the timer task's command queue length (see configTIMER_QUEUE_LENGTH documentation for more\ndetails).", "id": "FREERTOS_TIMER_QUEUE_LENGTH", "name": "FREERTOS_TIMER_QUEUE_LENGTH", "range": [5, 20], "title": "configTIMER_QUEUE_LENGTH", "type": "int"}, {"children": [], "depends_on": null, "help": "Set the size of the queue registry (see configQUEUE_REGISTRY_SIZE documentation for more details).\n\nNote: A value of 0 will disable queue registry functionality", "id": "FREERTOS_QUEUE_REGISTRY_SIZE", "name": "FREERTOS_QUEUE_REGISTRY_SIZE", "range": [0, 20], "title": "configQUEUE_REGISTRY_SIZE", "type": "int"}, {"children": [], "depends_on": null, "help": "Set the size of the task notification array of each task. When increasing this value, keep in\nmind that this means additional memory for each and every task on the system.\nHowever, task notifications in general are more light weight compared to alternatives\nsuch as semaphores.", "id": "FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES", "name": "FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES", "range": [1, 32], "title": "configTASK_NOTIFICATION_ARRAY_ENTRIES", "type": "int"}, {"children": [{"children": [{"children": [], "depends_on": "!FREERTOS_SMP && FREERTOS_USE_STATS_FORMATTING_FUNCTIONS", "help": "If enabled, this will include an extra column when vTaskL<PERSON> is called to display the CoreID the task\nis pinned to (0,1) or -1 if not pinned.", "id": "FREERTOS_VTASKLIST_INCLUDE_COREID", "name": "FREERTOS_VTASKLIST_INCLUDE_COREID", "range": null, "title": "Enable display of xCoreID in vTaskList", "type": "bool"}], "depends_on": "FREERTOS_USE_TRACE_FACILITY", "help": "Set configUSE_TRACE_FACILITY and configUSE_STATS_FORMATTING_FUNCTIONS to 1 to include the\n``vTaskList()`` and ``vTaskGetRunTimeStats()`` functions in the build (see\nconfigUSE_STATS_FORMATTING_FUNCTIONS documentation for more details).", "id": "FREERTOS_USE_STATS_FORMATTING_FUNCTIONS", "name": "FREERTOS_USE_STATS_FORMATTING_FUNCTIONS", "range": null, "title": "configUSE_STATS_FORMATTING_FUNCTIONS", "type": "bool"}], "depends_on": null, "help": "Enables additional structure members and functions to assist with execution visualization and tracing\n(see configUSE_TRACE_FACILITY documentation for more details).", "id": "FREERTOS_USE_TRACE_FACILITY", "name": "FREERTOS_USE_TRACE_FACILITY", "range": null, "title": "configUSE_TRACE_FACILITY", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enables collection of run time statistics for each task (see configGENERATE_RUN_TIME_STATS\ndocumentation for more details).\n\nNote: The clock used for run time statistics can be configured in FREERTOS_RUN_TIME_STATS_CLK.", "id": "FREERTOS_GENERATE_RUN_TIME_STATS", "name": "FREERTOS_GENERATE_RUN_TIME_STATS", "range": null, "title": "configGENERATE_RUN_TIME_STATS", "type": "bool"}, {"children": [{"children": [], "depends_on": "FREERTOS_USE_TICKLESS_IDLE", "help": "FreeRTOS will enter light sleep mode if no tasks need to run for this number of ticks.\nYou can enable PM_PROFILING feature in esp_pm components and dump the sleep status with\nesp_pm_dump_locks, if the proportion of rejected sleeps is too high, please increase\nthis value to improve scheduling efficiency", "id": "FREERTOS_IDLE_TIME_BEFORE_SLEEP", "name": "FREERTOS_IDLE_TIME_BEFORE_SLEEP", "range": null, "title": "configEXPECTED_IDLE_TIME_BEFORE_SLEEP", "type": "int"}], "depends_on": "PM_ENABLE", "help": "If power management support is enabled, FreeRTOS will be able to put the system into light sleep mode\nwhen no tasks need to run for a number of ticks. This number can be set using\nFREERTOS_IDLE_TIME_BEFORE_SLEEP option. This feature is also known as \"automatic light sleep\".\n\nNote that timers created using esp_timer APIs may prevent the system from entering sleep mode, even\nwhen no tasks need to run. To skip unnecessary wake-up initialize a timer with the\n\"skip_unhandled_events\" option as true.\n\nIf disabled, automatic light sleep support will be disabled.", "id": "FREERTOS_USE_TICKLESS_IDLE", "name": "FREERTOS_USE_TICKLESS_IDLE", "range": null, "title": "configUSE_TICKLESS_IDLE", "type": "bool"}], "depends_on": null, "id": "component-config-freertos-kernel", "title": "<PERSON><PERSON>", "type": "menu"}, {"children": [{"children": [], "depends_on": "COMPILER_OPTIMIZATION_DEFAULT || ESP_COREDUMP_ENABLE || ESP_GDBSTUB_ENABLED", "help": "If enabled, all FreeRTOS task functions will be enclosed in a wrapper function. If a task function\nmistakenly returns (i.e. does not delete), the call flow will return to the wrapper function. The\nwrapper function will then log an error and abort the application. This option is also required for GDB\nbacktraces and C++ exceptions to work correctly inside top-level task functions.", "id": "FREERTOS_TASK_FUNCTION_WRAPPER", "name": "FREERTOS_TASK_FUNCTION_WRAPPER", "range": null, "title": "Wrap task functions", "type": "bool"}, {"children": [], "depends_on": null, "help": "FreeRTOS can check if a stack has overflown its bounds by checking either the value of the stack\npointer or by checking the integrity of canary bytes. (See FREERTOS_CHECK_STACKOVERFLOW for more\ninformation.) These checks only happen on a context switch, and the situation that caused the stack\noverflow may already be long gone by then. This option will use the last debug memory watchpoint to\nallow breaking into the debugger (or panic'ing) as soon as any of the last 32 bytes on the stack of a\ntask are overwritten. The side effect is that using gdb, you effectively have one hardware watchpoint\nless because the last one is overwritten as soon as a task switch happens.\n\nAnother consequence is that due to alignment requirements of the watchpoint, the usable stack size\ndecreases by up to 60 bytes. This is because the watchpoint region has to be aligned to its size and\nthe size for the stack watchpoint in IDF is 32 bytes.\n\nThis check only triggers if the stack overflow writes within 32 bytes near the end of the stack, rather\nthan overshooting further, so it is worth combining this approach with one of the other stack overflow\ncheck methods.\n\nWhen this watchpoint is hit, gdb will stop with a SIGTRAP message. When no JTAG OCD is attached,\nesp-idf will panic on an unhandled debug exception.", "id": "FREERTOS_WATCHPOINT_END_OF_STACK", "name": "FREERTOS_WATCHPOINT_END_OF_STACK", "range": null, "title": "Enable stack overflow debug watchpoint", "type": "bool"}, {"children": [], "depends_on": "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS > 0", "help": "ESP-IDF provides users with the ability to free TLSP memory by registering TLSP deletion callbacks.\nThese callbacks are automatically called by FreeRTOS when a task is deleted. When this option is turned\non, the memory reserved for TLSPs in the TCB is doubled to make space for storing the deletion\ncallbacks. If the user does not wish to use TLSP deletion callbacks then this option could be turned\noff to save space in the TCB memory.", "id": "FREERTOS_TLSP_DELETION_CALLBACKS", "name": "FREERTOS_TLSP_DELETION_CALLBACKS", "range": null, "title": "Enable thread local storage pointers deletion callbacks", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enable this option to make FreeRTOS call the static task clean up hook when a task is deleted.\n\nNote: Users will need to provide a ``void vPortCleanUpTCB ( void *pxTCB )`` callback", "id": "FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP", "name": "FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP", "range": null, "title": "Enable static task clean up hook", "type": "bool"}, {"children": [], "depends_on": "!FREERTOS_SMP", "help": "If enabled, assert that when a mutex semaphore is given, the task giving the semaphore is the task\nwhich is currently holding the mutex.", "id": "FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER", "name": "FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER", "range": null, "title": "Check that mutex semaphore is given by owner task", "type": "bool"}, {"children": [], "depends_on": null, "help": "The interrupt handlers have their own stack. The size of the stack can be defined here. Each processor\nhas its own stack, so the total size occupied will be twice this.", "id": "FREERTOS_ISR_STACKSIZE", "name": "FREERTOS_ISR_STACKSIZE", "range": [1536, 32768], "title": "ISR stack size", "type": "int"}, {"children": [], "depends_on": null, "help": "If this option is enabled, interrupt stack frame will be modified to point to the code of the\ninterrupted task as its return address. This helps the debugger (or the panic handler) show a backtrace\nfrom the interrupt to the task which was interrupted. This also works for nested interrupts: higher\nlevel interrupt stack can be traced back to the lower level interrupt. This option adds 4 instructions\nto the interrupt dispatching code.", "id": "FREERTOS_INTERRUPT_BACKTRACE", "name": "FREERTOS_INTERRUPT_BACKTRACE", "range": null, "title": "Enable backtrace from interrupt to task context", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32", "help": "When enabled, the usage of float type is allowed inside Level 1 ISRs. Note that usage of float types in\nhigher level interrupts is still not permitted.", "id": "FREERTOS_FPU_IN_ISR", "name": "FREERTOS_FPU_IN_ISR", "range": null, "title": "Use float in Level 1 ISR", "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_TICK_SUPPORT_CORETIMER", "name": "FREERTOS_TICK_SUPPORT_CORETIMER", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_TICK_SUPPORT_SYSTIMER", "name": "FREERTOS_TICK_SUPPORT_SYSTIMER", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [], "depends_on": "FREERTOS_TICK_SUPPORT_CORETIMER && <choice FREERTOS_CORETIMER>", "help": "Select this to use timer 0", "id": "FREERTOS_CORETIMER_0", "name": "FREERTOS_CORETIMER_0", "range": null, "title": "Timer 0 (int 6, level 1)", "type": "bool"}, {"children": [], "depends_on": "FREERTOS_TICK_SUPPORT_CORETIMER && <choice FREERTOS_CORETIMER>", "help": "Select this to use timer 1", "id": "FREERTOS_CORETIMER_1", "name": "FREERTOS_CORETIMER_1", "range": null, "title": "Timer 1 (int 15, level 3)", "type": "bool"}, {"children": [], "depends_on": "FREERTOS_TICK_SUPPORT_SYSTIMER && <choice FREERTOS_CORETIMER>", "help": "Select this to use systimer with the 1 interrupt priority.", "id": "FREERTOS_CORETIMER_SYSTIMER_LVL1", "name": "FREERTOS_CORETIMER_SYSTIMER_LVL1", "range": null, "title": "SYSTIMER 0 (level 1)", "type": "bool"}, {"children": [], "depends_on": "FREERTOS_TICK_SUPPORT_SYSTIMER && <choice FREERTOS_CORETIMER>", "help": "Select this to use systimer with the 3 interrupt priority.", "id": "FREERTOS_CORETIMER_SYSTIMER_LVL3", "name": "FREERTOS_CORETIMER_SYSTIMER_LVL3", "range": null, "title": "SYSTIMER 0 (level 3)", "type": "bool"}], "depends_on": null, "help": "FreeRTOS needs a timer with an associated interrupt to use as the main tick source to increase\ncounters, run timers and do pre-emptive multitasking with. There are multiple timers available to do\nthis, with different interrupt priorities.", "id": "component-config-freertos-port-tick-timer-source-xtensa-only-", "name": "FREERTOS_CORETIMER", "title": "Tick timer source (Xtensa Only)", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_SYSTICK_USES_SYSTIMER", "name": "FREERTOS_SYSTICK_USES_SYSTIMER", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_SYSTICK_USES_CCOUNT", "name": "FREERTOS_SYSTICK_USES_CCOUNT", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice FREERTOS_RUN_TIME_STATS_CLK>", "help": "ESP Timer will be used as the clock source for FreeRTOS run time stats. The ESP Timer runs at a\nfrequency of 1MHz regardless of Dynamic Frequency Scaling. Therefore the ESP Timer will overflow in\napproximately 4290 seconds.", "id": "FREERTOS_RUN_TIME_STATS_USING_ESP_TIMER", "name": "FREERTOS_RUN_TIME_STATS_USING_ESP_TIMER", "range": null, "title": "Use ESP TIMER for run time stats", "type": "bool"}, {"children": [], "depends_on": "FREERTOS_SYSTICK_USES_CCOUNT && <choice FREERTOS_RUN_TIME_STATS_CLK>", "help": "CPU Clock will be used as the clock source for the generation of run time stats. The CPU Clock has\na frequency dependent on ESP_DEFAULT_CPU_FREQ_MHZ and Dynamic Frequency Scaling (DFS). Therefore\nthe CPU Clock frequency can fluctuate between 80 to 240MHz. Run time stats generated using the CPU\nClock represents the number of CPU cycles each task is allocated and DOES NOT reflect the amount of\ntime each task runs for (as CPU clock frequency can change). If the CPU clock consistently runs at\nthe maximum frequency of 240MHz, it will overflow in approximately 17 seconds.", "id": "FREERTOS_RUN_TIME_STATS_USING_CPU_CLK", "name": "FREERTOS_RUN_TIME_STATS_USING_CPU_CLK", "range": null, "title": "Use CPU Clock for run time stats", "type": "bool"}], "depends_on": "FREERTOS_GENERATE_RUN_TIME_STATS", "help": "Choose the clock source for FreeRTOS run time stats. Options are CPU0's CPU Clock or the ESP Timer.\nBoth clock sources are 32 bits. The CPU Clock can run at a higher frequency hence provide a finer\nresolution but will overflow much quicker. Note that run time stats are only valid until the clock\nsource overflows.", "id": "component-config-freertos-port-choose-the-clock-source-for-run-time-stats", "name": "FREERTOS_RUN_TIME_STATS_CLK", "title": "Choose the clock source for run time stats", "type": "choice"}, {"children": [], "depends_on": null, "help": "When enabled the selected Non-ISR FreeRTOS functions will be placed into Flash memory instead of IRAM.\nThis saves up to 8KB of IRAM depending on which functions are used.", "id": "FREERTOS_PLACE_FUNCTIONS_INTO_FLASH", "name": "FREERTOS_PLACE_FUNCTIONS_INTO_FLASH", "range": null, "title": "Place FreeRTOS functions into Flash", "type": "bool"}, {"children": [], "depends_on": "FREERTOS_ENABLE_TASK_SNAPSHOT && !ESP_PANIC_HANDLER_IRAM", "help": "When enabled, the functions related to snapshots, such as vTaskGetSnapshot or uxTaskGetSnapshotAll,\nwill be placed in flash. Note that if enabled, these functions cannot be called when cache is disabled.", "id": "FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH", "name": "FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH", "range": null, "title": "Place task snapshot functions into flash", "type": "bool"}, {"children": [], "depends_on": null, "help": "If enabled, context of port*_CRITICAL calls (ISR or Non-ISR) would be checked to be in compliance with\nVanilla FreeRTOS. e.g Calling port*_CRITICAL from ISR context would cause assert failure", "id": "FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE", "name": "FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE", "range": null, "title": "Tests compliance with Vanilla FreeRTOS port*_CRITICAL calls", "type": "bool"}, {"children": [], "depends_on": null, "help": "When enabled, the functions related to snapshots, such as vTaskGetSnapshot or uxTaskGetSnapshotAll, are\ncompiled and linked. Task snapshots are used by Task Watchdog (TWDT), GDB Stub and Core dump.", "id": "FREERTOS_ENABLE_TASK_SNAPSHOT", "name": "FREERTOS_ENABLE_TASK_SNAPSHOT", "range": null, "title": "Enable task snapshot functions", "type": "bool"}], "depends_on": null, "id": "component-config-freertos-port", "title": "Port", "type": "menu"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_NO_AFFINITY", "name": "FREERTOS_NO_AFFINITY", "range": null, "title": null, "type": "hex"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_SUPPORT_STATIC_ALLOCATION", "name": "FREERTOS_SUPPORT_STATIC_ALLOCATION", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": "Hidden option, gets selected by CONFIG_ESP_DEBUG_OCDAWARE", "id": "FREERTOS_DEBUG_OCDAWARE", "name": "FREERTOS_DEBUG_OCDAWARE", "range": null, "title": null, "type": "bool"}], "depends_on": null, "id": "component-config-freertos", "title": "FreeRTOS", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice HAL_DEFAULT_ASSERTION_LEVEL>", "help": null, "id": "HAL_ASSERTION_EQUALS_SYSTEM", "name": "HAL_ASSERTION_EQUALS_SYSTEM", "range": null, "title": "Same as system assertion level", "type": "bool"}, {"children": [], "depends_on": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL >= 0 && <choice HAL_DEFAULT_ASSERTION_LEVEL>", "help": null, "id": "HAL_ASSERTION_DISABLE", "name": "HAL_ASSERTION_DISABLE", "range": null, "title": "Disabled", "type": "bool"}, {"children": [], "depends_on": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL >= 1 && <choice HAL_DEFAULT_ASSERTION_LEVEL>", "help": null, "id": "HAL_ASSERTION_SILENT", "name": "HAL_ASSERTION_SILENT", "range": null, "title": "Silent", "type": "bool"}, {"children": [], "depends_on": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL >= 2 && <choice HAL_DEFAULT_ASSERTION_LEVEL>", "help": null, "id": "HAL_ASSERTION_ENABLE", "name": "HAL_ASSERTION_ENABLE", "range": null, "title": "Enabled", "type": "bool"}], "depends_on": null, "help": "Set the assert behavior / level for HAL component.\nHAL component assert level can be set separately,\nbut the level can't exceed the system assertion level.\ne.g. If the system assertion is disabled, then the HAL\nassertion can't be enabled either. If the system assertion\nis enable, then the HAL assertion can still be disabled\nby this Kconfig option.", "id": "component-config-hardware-abstraction-layer-hal-and-low-level-ll--default-hal-assertion-level", "name": "HAL_DEFAULT_ASSERTION_LEVEL", "title": "Default HAL assertion level", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "HAL_DEFAULT_ASSERTION_LEVEL", "name": "HAL_DEFAULT_ASSERTION_LEVEL", "range": null, "title": null, "type": "int"}, {"children": [{"children": [], "depends_on": "<choice HAL_LOG_LEVEL>", "help": null, "id": "HAL_LOG_LEVEL_NONE", "name": "HAL_LOG_LEVEL_NONE", "range": null, "title": "No output", "type": "bool"}, {"children": [], "depends_on": "<choice HAL_LOG_LEVEL>", "help": null, "id": "HAL_LOG_LEVEL_ERROR", "name": "HAL_LOG_LEVEL_ERROR", "range": null, "title": "Error", "type": "bool"}, {"children": [], "depends_on": "<choice HAL_LOG_LEVEL>", "help": null, "id": "HAL_LOG_LEVEL_WARN", "name": "HAL_LOG_LEVEL_WARN", "range": null, "title": "Warning", "type": "bool"}, {"children": [], "depends_on": "<choice HAL_LOG_LEVEL>", "help": null, "id": "HAL_LOG_LEVEL_INFO", "name": "HAL_LOG_LEVEL_INFO", "range": null, "title": "Info", "type": "bool"}, {"children": [], "depends_on": "<choice HAL_LOG_LEVEL>", "help": null, "id": "HAL_LOG_LEVEL_DEBUG", "name": "HAL_LOG_LEVEL_DEBUG", "range": null, "title": "Debug", "type": "bool"}, {"children": [], "depends_on": "<choice HAL_LOG_LEVEL>", "help": null, "id": "HAL_LOG_LEVEL_VERBOSE", "name": "HAL_LOG_LEVEL_VERBOSE", "range": null, "title": "Verbose", "type": "bool"}], "depends_on": "!LOG_DEFAULT_LEVEL_NONE && !LOG_DEFAULT_LEVEL_ERROR && !LOG_DEFAULT_LEVEL_WARN && !LOG_DEFAULT_LEVEL_INFO && !LOG_DEFAULT_LEVEL_DEBUG && !LOG_DEFAULT_LEVEL_VERBOSE", "help": "Specify how much output to see in HAL logs.", "id": "component-config-hardware-abstraction-layer-hal-and-low-level-ll--hal-layer-log-verbosity", "name": "HAL_LOG_LEVEL", "title": "HAL layer log verbosity", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "HAL_LOG_LEVEL", "name": "HAL_LOG_LEVEL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": "ESP_ROM_HAS_HAL_SYSTIMER", "help": "Enable this flag to use HAL functions from ROM instead of ESP-IDF.\n\nIf keeping this as \"n\" in your project, you will have less free IRAM.\nIf making this as \"y\" in your project, you will increase free IRAM,\nbut you will lose the possibility to debug this module, and some new\nfeatures will be added and bugs will be fixed in the IDF source\nbut cannot be synced to ROM.", "id": "HAL_SYSTIMER_USE_ROM_IMPL", "name": "HAL_SYSTIMER_USE_ROM_IMPL", "range": null, "title": "Use ROM implementation of SysTimer HAL driver", "type": "bool"}, {"children": [], "depends_on": "ESP_ROM_HAS_HAL_WDT", "help": "Enable this flag to use HAL functions from ROM instead of ESP-IDF.\n\nIf keeping this as \"n\" in your project, you will have less free IRAM.\nIf making this as \"y\" in your project, you will increase free IRAM,\nbut you will lose the possibility to debug this module, and some new\nfeatures will be added and bugs will be fixed in the IDF source\nbut cannot be synced to ROM.", "id": "HAL_WDT_USE_ROM_IMPL", "name": "HAL_WDT_USE_ROM_IMPL", "range": null, "title": "Use ROM implementation of WDT HAL driver", "type": "bool"}, {"children": [], "depends_on": "SPI_MASTER_ISR_IN_IRAM", "help": "Enable this option to place SPI master hal layer functions into IRAM.", "id": "HAL_SPI_MASTER_FUNC_IN_IRAM", "name": "HAL_SPI_MASTER_FUNC_IN_IRAM", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "SPI_SLAVE_ISR_IN_IRAM", "help": "Enable this option to place SPI slave hal layer functions into IRAM.", "id": "HAL_SPI_SLAVE_FUNC_IN_IRAM", "name": "HAL_SPI_SLAVE_FUNC_IN_IRAM", "range": null, "title": null, "type": "bool"}], "depends_on": null, "id": "component-config-hardware-abstraction-layer-hal-and-low-level-ll-", "title": "Hardware Abstraction Layer (HAL) and Low Level (LL)", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_NONE", "name": "LOG_DEFAULT_LEVEL_NONE", "range": null, "title": "No output", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_ERROR", "name": "LOG_DEFAULT_LEVEL_ERROR", "range": null, "title": "Error", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_WARN", "name": "LOG_DEFAULT_LEVEL_WARN", "range": null, "title": "Warning", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_INFO", "name": "LOG_DEFAULT_LEVEL_INFO", "range": null, "title": "Info", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_DEBUG", "name": "LOG_DEFAULT_LEVEL_DEBUG", "range": null, "title": "Debug", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_VERBOSE", "name": "LOG_DEFAULT_LEVEL_VERBOSE", "range": null, "title": "Verbose", "type": "bool"}], "depends_on": null, "help": "Specify how much output to see in logs by default.\nYou can set lower verbosity level at runtime using\nesp_log_level_set function.\n\nBy default, this setting limits which log statements\nare compiled into the program. For example, selecting\n\"Warning\" would mean that changing log level to \"Debug\"\nat runtime will not be possible. To allow increasing log\nlevel above the default at runtime, see the next option.", "id": "component-config-log-output-default-log-verbosity", "name": "LOG_DEFAULT_LEVEL", "title": "Default log verbosity", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "LOG_DEFAULT_LEVEL", "name": "LOG_DEFAULT_LEVEL", "range": null, "title": null, "type": "int"}, {"children": [{"children": [], "depends_on": "<choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_EQUALS_DEFAULT", "name": "LOG_MAXIMUM_EQUALS_DEFAULT", "range": null, "title": "Same as default", "type": "bool"}, {"children": [], "depends_on": "LOG_DEFAULT_LEVEL < 1 && <choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_LEVEL_ERROR", "name": "LOG_MAXIMUM_LEVEL_ERROR", "range": null, "title": "Error", "type": "bool"}, {"children": [], "depends_on": "LOG_DEFAULT_LEVEL < 2 && <choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_LEVEL_WARN", "name": "LOG_MAXIMUM_LEVEL_WARN", "range": null, "title": "Warning", "type": "bool"}, {"children": [], "depends_on": "LOG_DEFAULT_LEVEL < 3 && <choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_LEVEL_INFO", "name": "LOG_MAXIMUM_LEVEL_INFO", "range": null, "title": "Info", "type": "bool"}, {"children": [], "depends_on": "LOG_DEFAULT_LEVEL < 4 && <choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_LEVEL_DEBUG", "name": "LOG_MAXIMUM_LEVEL_DEBUG", "range": null, "title": "Debug", "type": "bool"}, {"children": [], "depends_on": "LOG_DEFAULT_LEVEL < 5 && <choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_LEVEL_VERBOSE", "name": "LOG_MAXIMUM_LEVEL_VERBOSE", "range": null, "title": "Verbose", "type": "bool"}], "depends_on": null, "help": "This config option sets the highest log verbosity that it's possible to select\nat runtime by calling esp_log_level_set(). This level may be higher than\nthe default verbosity level which is set when the app starts up.\n\nThis can be used enable debugging output only at a critical point, for a particular\ntag, or to minimize startup time but then enable more logs once the firmware has\nloaded.\n\nNote that increasing the maximum available log level will increase the firmware\nbinary size.\n\nThis option only applies to logging from the app, the bootloader log level is\nfixed at compile time to the separate \"Bootloader log verbosity\" setting.", "id": "component-config-log-output-maximum-log-verbosity", "name": "LOG_MAXIMUM_LEVEL", "title": "Maximum log verbosity", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "LOG_MAXIMUM_LEVEL", "name": "LOG_MAXIMUM_LEVEL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": "Enable ANSI terminal color codes in bootloader output.\n\nIn order to view these, your terminal program must support ANSI color codes.", "id": "LOG_COLORS", "name": "LOG_COLORS", "range": null, "title": "Use ANSI terminal colors in log output", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice LOG_TIMESTAMP_SOURCE>", "help": null, "id": "LOG_TIMESTAMP_SOURCE_RTOS", "name": "LOG_TIMESTAMP_SOURCE_RTOS", "range": null, "title": "Milliseconds Since <PERSON><PERSON>", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_TIMESTAMP_SOURCE>", "help": null, "id": "LOG_TIMESTAMP_SOURCE_SYSTEM", "name": "LOG_TIMESTAMP_SOURCE_SYSTEM", "range": null, "title": "System Time", "type": "bool"}], "depends_on": null, "help": "Choose what sort of timestamp is displayed in the log output:\n\n- Milliseconds since boot is calulated from the RTOS tick count multiplied\n  by the tick period. This time will reset after a software reboot.\n  e.g. (90000)\n\n- System time is taken from POSIX time functions which use the chip's\n  RTC and high resoultion timers to maintain an accurate time. The system time is\n  initialized to 0 on startup, it can be set with an SNTP sync, or with\n  POSIX time functions. This time will not reset after a software reboot.\n  e.g. (00:01:30.000)\n\n- NOTE: Currently this will not get used in logging from binary blobs\n  (i.e WiFi & Bluetooth libraries), these will always print\n  milliseconds since boot.", "id": "component-config-log-output-log-timestamps", "name": "LOG_TIMESTAMP_SOURCE", "title": "Log Timestamps", "type": "choice"}], "depends_on": null, "id": "component-config-log-output", "title": "Log output", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice NEWLIB_STDOUT_LINE_ENDING>", "help": null, "id": "NEWLIB_STDOUT_LINE_ENDING_CRLF", "name": "NEWLIB_STDOUT_LINE_ENDING_CRLF", "range": null, "title": "CRLF", "type": "bool"}, {"children": [], "depends_on": "<choice NEWLIB_STDOUT_LINE_ENDING>", "help": null, "id": "NEWLIB_STDOUT_LINE_ENDING_LF", "name": "NEWLIB_STDOUT_LINE_ENDING_LF", "range": null, "title": "LF", "type": "bool"}, {"children": [], "depends_on": "<choice NEWLIB_STDOUT_LINE_ENDING>", "help": null, "id": "NEWLIB_STDOUT_LINE_ENDING_CR", "name": "NEWLIB_STDOUT_LINE_ENDING_CR", "range": null, "title": "CR", "type": "bool"}], "depends_on": null, "help": "This option allows configuring the desired line endings sent to UART\nwhen a newline ('\\n', LF) appears on stdout.\nThree options are possible:\n\nCRLF: whenever LF is encountered, prepend it with CR\n\nLF: no modification is applied, stdout is sent as is\n\nCR: each occurence of LF is replaced with CR\n\nThis option doesn't affect behavior of the UART driver (drivers/uart.h).", "id": "component-config-newlib-line-ending-for-uart-output", "name": "NEWLIB_STDOUT_LINE_ENDING", "title": "Line ending for UART output", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice NEWLIB_STDIN_LINE_ENDING>", "help": null, "id": "NEWLIB_STDIN_LINE_ENDING_CRLF", "name": "NEWLIB_STDIN_LINE_ENDING_CRLF", "range": null, "title": "CRLF", "type": "bool"}, {"children": [], "depends_on": "<choice NEWLIB_STDIN_LINE_ENDING>", "help": null, "id": "NEWLIB_STDIN_LINE_ENDING_LF", "name": "NEWLIB_STDIN_LINE_ENDING_LF", "range": null, "title": "LF", "type": "bool"}, {"children": [], "depends_on": "<choice NEWLIB_STDIN_LINE_ENDING>", "help": null, "id": "NEWLIB_STDIN_LINE_ENDING_CR", "name": "NEWLIB_STDIN_LINE_ENDING_CR", "range": null, "title": "CR", "type": "bool"}], "depends_on": null, "help": "This option allows configuring which input sequence on UART produces\na newline ('\\n', LF) on stdin.\nThree options are possible:\n\nCRLF: CRLF is converted to LF\n\nLF: no modification is applied, input is sent to stdin as is\n\nCR: each occurence of CR is replaced with LF\n\nThis option doesn't affect behavior of the UART driver (drivers/uart.h).", "id": "component-config-newlib-line-ending-for-uart-input", "name": "NEWLIB_STDIN_LINE_ENDING", "title": "Line ending for UART input", "type": "choice"}, {"children": [], "depends_on": null, "help": "In most chips the ROM contains parts of newlib C library, including printf/scanf family\nof functions. These functions have been compiled with so-called \"nano\"\nformatting option. This option doesn't support 64-bit integer formats and C99\nfeatures, such as positional arguments.\n\nFor more details about \"nano\" formatting option, please see newlib readme file,\nsearch for '--enable-newlib-nano-formatted-io':\nhttps://sourceware.org/newlib/README\n\nIf this option is enabled and the ROM contains functions from newlib-nano, the build system\nwill use functions available in ROM, reducing the application binary size.\nFunctions available in ROM run faster than functions which run from flash. Functions available\nin ROM can also run when flash instruction cache is disabled.\n\nSome chips (e.g. ESP32-C6) has the full formatting versions of printf/scanf in ROM instead of\nthe nano versions and in this building with newlib nano might actually increase the size of\nthe binary. Which functions are present in ROM can be seen from ROM caps:\nESP_ROM_HAS_NEWLIB_NANO_FORMAT and ESP_ROM_HAS_NEWLIB_NORMAL_FORMAT.\n\nIf you need 64-bit integer formatting support or C99 features, keep this\noption disabled.", "id": "NEWLIB_NANO_FORMAT", "name": "NEWLIB_NANO_FORMAT", "range": null, "title": "Enable 'nano' formatting options for printf/scanf family", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice NEWLIB_TIME_SYSCALL>", "help": null, "id": "NEWLIB_TIME_SYSCALL_USE_RTC_HRT", "name": "NEWLIB_TIME_SYSCALL_USE_RTC_HRT", "range": null, "title": "RTC and high-resolution timer", "type": "bool"}, {"children": [], "depends_on": "<choice NEWLIB_TIME_SYSCALL>", "help": null, "id": "NEWLIB_TIME_SYSCALL_USE_RTC", "name": "NEWLIB_TIME_SYSCALL_USE_RTC", "range": null, "title": "RTC", "type": "bool"}, {"children": [], "depends_on": "<choice NEWLIB_TIME_SYSCALL>", "help": null, "id": "NEWLIB_TIME_SYSCALL_USE_HRT", "name": "NEWLIB_TIME_SYSCALL_USE_HRT", "range": null, "title": "High-resolution timer", "type": "bool"}, {"children": [], "depends_on": "<choice NEWLIB_TIME_SYSCALL>", "help": null, "id": "NEWLIB_TIME_SYSCALL_USE_NONE", "name": "NEWLIB_TIME_SYSCALL_USE_NONE", "range": null, "title": "None", "type": "bool"}], "depends_on": null, "help": "This setting defines which hardware timers are used to\nimplement 'gettimeofday' and 'time' functions in C library.\n\n- If both high-resolution (systimer for all targets except ESP32)\n    and RTC timers are used, timekeeping will continue in deep sleep.\n    Time will be reported at 1 microsecond resolution.\n    This is the default, and the recommended option.\n- If only high-resolution timer (systimer) is used, gettimeofday will\n    provide time at microsecond resolution.\n    Time will not be preserved when going into deep sleep mode.\n- If only RTC timer is used, timekeeping will continue in\n    deep sleep, but time will be measured at 6.(6) microsecond\n    resolution. Also the gettimeofday function itself may take\n    longer to run.\n- If no timers are used, gettimeofday and time functions\n    return -1 and set errno to ENOSYS.\n- When RTC is used for timekeeping, two RTC_STORE registers are\n    used to keep time in deep sleep mode.", "id": "component-config-newlib-timers-used-for-gettimeofday-function", "name": "NEWLIB_TIME_SYSCALL", "title": "Timers used for gettimeofday function", "type": "choice"}], "depends_on": null, "id": "component-config-newlib", "title": "New<PERSON>b", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": null, "help": null, "id": "MMU_PAGE_SIZE_16KB", "name": "MMU_PAGE_SIZE_16KB", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "MMU_PAGE_SIZE_32KB", "name": "MMU_PAGE_SIZE_32KB", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "MMU_PAGE_SIZE_64KB", "name": "MMU_PAGE_SIZE_64KB", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "MMU_PAGE_MODE", "name": "MMU_PAGE_MODE", "range": null, "title": null, "type": "string"}, {"children": [], "depends_on": null, "help": null, "id": "MMU_PAGE_SIZE", "name": "MMU_PAGE_SIZE", "range": null, "title": null, "type": "hex"}], "depends_on": null, "id": "component-config-soc-settings-mmu-config", "title": "MMU Config", "type": "menu"}], "depends_on": null, "id": "component-config-soc-settings", "title": "SoC Settings", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "SPI_FLASH_VERIFY_WRITE && !APP_BUILD_TYPE_PURE_RAM_APP", "help": "If this option is enabled, if SPI flash write verification fails then a log error line\nwill be written with the address, expected & actual values. This can be useful when\ndebugging hardware SPI flash problems.", "id": "SPI_FLASH_LOG_FAILED_WRITE", "name": "SPI_FLASH_LOG_FAILED_WRITE", "range": null, "title": "Log errors if verification fails", "type": "bool"}, {"children": [], "depends_on": "SPI_FLASH_VERIFY_WRITE && !APP_BUILD_TYPE_PURE_RAM_APP", "help": "If this option is enabled, any SPI flash write which tries to set zero bits in the flash to\nones will log a warning. Such writes will not result in the requested data appearing identically\nin flash once written, as SPI NOR flash can only set bits to one when an entire sector is erased.\nAfter erasing, individual bits can only be written from one to zero.\n\nNote that some software (such as SPIFFS) which is aware of SPI NOR flash may write one bits as an\noptimisation, relying on the data in flash becoming a bitwise AND of the new data and any existing data.\nSuch software will log spurious warnings if this option is enabled.", "id": "SPI_FLASH_WARN_SETTING_ZERO_TO_ONE", "name": "SPI_FLASH_WARN_SETTING_ZERO_TO_ONE", "range": null, "title": "Log warning if writing zero bits to ones", "type": "bool"}], "depends_on": "!SPI_FLASH_ROM_IMPL && !APP_BUILD_TYPE_PURE_RAM_APP", "help": "If this option is enabled, any time SPI flash is written then the data will be read\nback and verified. This can catch hardware problems with SPI flash, or flash which\nwas not erased before verification.", "id": "SPI_FLASH_VERIFY_WRITE", "name": "SPI_FLASH_VERIFY_WRITE", "range": null, "title": "Verify SPI flash writes", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "This option enables the following APIs:\n\n- esp_flash_reset_counters\n- esp_flash_dump_counters\n- esp_flash_get_counters\n\nThese APIs may be used to collect performance data for spi_flash APIs\nand to help understand behaviour of libraries which use SPI flash.", "id": "SPI_FLASH_ENABLE_COUNTERS", "name": "SPI_FLASH_ENABLE_COUNTERS", "range": null, "title": "Enable operation counters", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Enable this flag to use patched versions of SPI flash ROM driver functions.\nThis option should be enabled, if any one of the following is true: (1) need to write\nto flash on ESP32-D2WD; (2) main SPI flash is connected to non-default pins; (3) main\nSPI flash chip is manufactured by ISSI.", "id": "SPI_FLASH_ROM_DRIVER_PATCH", "name": "SPI_FLASH_ROM_DRIVER_PATCH", "range": null, "title": "Enable SPI flash ROM driver patched functions", "type": "bool"}, {"children": [], "depends_on": "ESP_ROM_HAS_SPI_FLASH && !APP_BUILD_TYPE_PURE_RAM_APP", "help": "Enable this flag to use new SPI flash driver functions from ROM instead of ESP-IDF.\n\nIf keeping this as \"n\" in your project, you will have less free IRAM.\nBut you can use all of our flash features.\n\nIf making this as \"y\" in your project, you will increase free IRAM.\nBut you may miss out on some flash features and support for new flash chips.\n\nCurrently the ROM cannot support the following features:\n\n- SPI_FLASH_AUTO_SUSPEND (C3, S3)", "id": "SPI_FLASH_ROM_IMPL", "name": "SPI_FLASH_ROM_IMPL", "range": null, "title": "Use esp_flash implementation in ROM", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice SPI_FLASH_DANGEROUS_WRITE>", "help": null, "id": "SPI_FLASH_DANGEROUS_WRITE_ABORTS", "name": "SPI_FLASH_DANGEROUS_WRITE_ABORTS", "range": null, "title": "Aborts", "type": "bool"}, {"children": [], "depends_on": "<choice SPI_FLASH_DANGEROUS_WRITE>", "help": null, "id": "SPI_FLASH_DANGEROUS_WRITE_FAILS", "name": "SPI_FLASH_DANGEROUS_WRITE_FAILS", "range": null, "title": "Fails", "type": "bool"}, {"children": [], "depends_on": "<choice SPI_FLASH_DANGEROUS_WRITE>", "help": null, "id": "SPI_FLASH_DANGEROUS_WRITE_ALLOWED", "name": "SPI_FLASH_DANGEROUS_WRITE_ALLOWED", "range": null, "title": "Allowed", "type": "bool"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "SPI flash APIs can optionally abort or return a failure code\nif erasing or writing addresses that fall at the beginning\nof flash (covering the bootloader and partition table) or that\noverlap the app partition that contains the running app.\n\nIt is not recommended to ever write to these regions from an IDF app,\nand this check prevents logic errors or corrupted firmware memory from\ndamaging these regions.\n\nNote that this feature *does not* check calls to the esp_rom_xxx SPI flash\nROM functions. These functions should not be called directly from IDF\napplications.", "id": "component-config-spi-flash-driver-writing-to-dangerous-flash-regions", "name": "SPI_FLASH_DANGEROUS_WRITE", "title": "Writing to dangerous flash regions", "type": "choice"}, {"children": [], "depends_on": "IDF_TARGET_ESP32 && !APP_BUILD_TYPE_PURE_RAM_APP", "help": "Each SPI bus needs a lock for arbitration among devices. This allows multiple\ndevices on a same bus, but may reduce the speed of esp_flash driver access to the\nmain flash chip.\n\nIf you only need to use esp_flash driver to access the main flash chip, disable\nthis option, and the lock will be bypassed on SPI1 bus. Otherwise if extra devices\nare needed to attach to SPI1 bus, enable this option.", "id": "SPI_FLASH_SHARE_SPI1_BUS", "name": "SPI_FLASH_SHARE_SPI1_BUS", "range": null, "title": "Support other devices attached to SPI1 bus", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Some flash chips can have very high \"max\" erase times, especially for block erase (32KB or 64KB).\nThis option allows to bypass \"block erase\" and always do sector erase commands.\nThis will be much slower overall in most cases, but improves latency for other code to run.", "id": "SPI_FLASH_BYPASS_BLOCK_ERASE", "name": "SPI_FLASH_BYPASS_BLOCK_ERASE", "range": null, "title": "Bypass a block erase and always do sector erase", "type": "bool"}, {"children": [{"children": [], "depends_on": "SPI_FLASH_YIELD_DURING_ERASE && !APP_BUILD_TYPE_PURE_RAM_APP", "help": "If a duration of one erase command is large\nthen it will yield CPUs after finishing a current command.", "id": "SPI_FLASH_ERASE_YIELD_DURATION_MS", "name": "SPI_FLASH_ERASE_YIELD_DURATION_MS", "range": null, "title": "Duration of erasing to yield CPUs (ms)", "type": "int"}, {"children": [], "depends_on": "SPI_FLASH_YIELD_DURING_ERASE && !APP_BUILD_TYPE_PURE_RAM_APP", "help": "Defines how many ticks will be before returning to continue a erasing.", "id": "SPI_FLASH_ERASE_YIELD_TICKS", "name": "SPI_FLASH_ERASE_YIELD_TICKS", "range": null, "title": "CPU release time (tick) for an erase operation", "type": "int"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "This allows to yield the CPUs between erase commands.\nPrevents starvation of other tasks.\nPlease use this configuration together with ``SPI_FLASH_ERASE_YIELD_DURATION_MS`` and\n``SPI_FLASH_ERASE_YIELD_TICKS`` after carefully checking flash datasheet to avoid a\nwatchdog timeout.\nFor more information, please check `SPI Flash API` reference documenation\nunder section `OS Function`.", "id": "SPI_FLASH_YIELD_DURING_ERASE", "name": "SPI_FLASH_YIELD_DURING_ERASE", "range": null, "title": "Enables yield operation during flash erase", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32C3 && !SPI_FLASH_ROM_IMPL && !APP_BUILD_TYPE_PURE_RAM_APP", "help": "This option is default n before ESP32-C3, because it needs bootloader support.\n\nCAUTION: If you want to OTA to an app with this feature turned on, please make\nsure the bootloader has the support for it. (later than IDF v4.3)\n\nAuto-suspend feature only supported by XMC chip.\nIf you are using an official module, please contact Espressif Business support.\nAlso reading auto suspend part in `SPI Flash API` document before you enable this function.", "id": "SPI_FLASH_AUTO_SUSPEND", "name": "SPI_FLASH_AUTO_SUSPEND", "range": null, "title": "Auto suspend long erase/write operations (READ DOCS FIRST)", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Flash write is broken down in terms of multiple (smaller) write operations.\nThis configuration options helps to set individual write chunk size, smaller\nvalue here ensures that cache (and non-IRAM resident interrupts) remains\ndisabled for shorter duration.", "id": "SPI_FLASH_WRITE_CHUNK_SIZE", "name": "SPI_FLASH_WRITE_CHUNK_SIZE", "range": [256, 8192], "title": "Flash write chunk size", "type": "int"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "SPI Flash driver uses the flash size configured in bootloader header by default.\nEnable this option to override flash size with latest ESPTOOLPY_FLASHSIZE value from\nthe app header if the size in the bootloader header is incorrect.", "id": "SPI_FLASH_SIZE_OVERRIDE", "name": "SPI_FLASH_SIZE_OVERRIDE", "range": null, "title": "Override flash size in bootloader header by ESPTOOLPY_FLASHSIZE", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "This option is helpful if you are using a flash chip whose timeout is quite large or unpredictable.", "id": "SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED", "name": "SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED", "range": null, "title": "Flash timeout checkout disabled", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "This option allows the chip driver list to be customized, instead of using the default list provided by\nESP-IDF.\n\nWhen this option is enabled, the default list is no longer compiled or linked. Instead, the\n`default_registered_chips` structure must be provided by the user.\n\nSee example: custom_chip_driver under examples/storage for more details.", "id": "SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST", "name": "SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST", "range": null, "title": "Override default chip driver list", "type": "bool"}, {"children": [{"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "When this option is selected, the patch will be enabled for XMC.\nFollow the recommended flow by XMC for better stability.\n\nDO NOT DISABLE UNLESS YOU KNOW WHAT YOU ARE DOING.", "id": "SPI_FLASH_BROWNOUT_RESET_XMC", "name": "SPI_FLASH_BROWNOUT_RESET_XMC", "range": null, "title": "Enable sending reset when brownout for XMC flash chips", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "When brownout happens during flash erase/write operations,\nsend reset command to stop the flash operations to improve stability.", "id": "SPI_FLASH_BROWNOUT_RESET", "name": "SPI_FLASH_BROWNOUT_RESET", "range": null, "title": null, "type": "bool"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "id": "component-config-spi-flash-driver-spi-flash-behavior-when-brownout", "title": "SPI Flash behavior when brownout", "type": "menu"}, {"children": [{"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": null, "id": "SPI_FLASH_VENDOR_XMC_SUPPORTED", "name": "SPI_FLASH_VENDOR_XMC_SUPPORTED", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Enable this to support auto detection of ISSI chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_ISSI_CHIP", "name": "SPI_FLASH_SUPPORT_ISSI_CHIP", "range": null, "title": "ISSI", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Enable this to support auto detection of MXIC chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_MXIC_CHIP", "name": "SPI_FLASH_SUPPORT_MXIC_CHIP", "range": null, "title": "MXIC", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Enable this to support auto detection of GD (GigaDevice) chips if chip vendor not\ndirectly given by ``chip_drv`` member of the chip struct. If you are using Wrover\nmodules, please don't disable this, otherwise your flash may not work in 4-bit\nmode.\n\nThis adds support for variant chips, however will extend detecting time and image\nsize. Note that the default chip driver supports the GD chips with product ID\n60H.", "id": "SPI_FLASH_SUPPORT_GD_CHIP", "name": "SPI_FLASH_SUPPORT_GD_CHIP", "range": null, "title": "GigaDevice", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Enable this to support auto detection of Winbond chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_WINBOND_CHIP", "name": "SPI_FLASH_SUPPORT_WINBOND_CHIP", "range": null, "title": "Winbond", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Enable this to support auto detection of BOYA chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_BOYA_CHIP", "name": "SPI_FLASH_SUPPORT_BOYA_CHIP", "range": null, "title": "BOYA", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "Enable this to support auto detection of TH chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_TH_CHIP", "name": "SPI_FLASH_SUPPORT_TH_CHIP", "range": null, "title": "TH", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32S3 && !APP_BUILD_TYPE_PURE_RAM_APP", "help": "Enable this to support auto detection of Octal MXIC chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_MXIC_OPI_CHIP", "name": "SPI_FLASH_SUPPORT_MXIC_OPI_CHIP", "range": null, "title": "mxic (opi)", "type": "bool"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "id": "component-config-spi-flash-driver-auto-detect-flash-chips", "title": "Auto-detect flash chips", "type": "menu"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "This option enables flash read/write operations to encrypted partition/s. This option\nis kept enabled irrespective of state of flash encryption feature. However, in case\napplication is not using flash encryption feature and is in need of some additional\nmemory from IRAM region (~1KB) then this config can be disabled.", "id": "SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE", "name": "SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE", "range": null, "title": "Enable encrypted partition read/write operations", "type": "bool"}, {"children": [], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "help": "This option is invisible, and will be selected automatically\nwhen ``ESPTOOLPY_FLASHFREQ_120M`` is selected.", "id": "SPI_FLASH_HPM_ENABLE", "name": "SPI_FLASH_HPM_ENABLE", "range": null, "title": null, "type": "bool"}], "depends_on": "!APP_BUILD_TYPE_PURE_RAM_APP", "id": "component-config-spi-flash-driver", "title": "SPI Flash driver", "type": "menu"}], "depends_on": null, "id": "component-config", "title": "Component config", "type": "menu"}, {"children": [], "depends_on": null, "help": "By enabling this option, ESP-IDF experimental feature options will be visible.\n\nNote you should still enable a certain experimental feature option to use it, and you\nshould read the corresponding risk warning and known issue list carefully.", "id": "IDF_EXPERIMENTAL_FEATURES", "name": "IDF_EXPERIMENTAL_FEATURES", "range": null, "title": "Make experimental features visible", "type": "bool"}]