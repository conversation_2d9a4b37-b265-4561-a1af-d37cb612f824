# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/esp-idf-anviz/components/bootloader/subproject

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test/coap/examples/coap_client/build/bootloader

# Include any dependencies generated for this target.
include esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.make

# Include the progress variables for this target.
include esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/progress.make

# Include the compile flags for this target's objects.
include esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_common.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_common.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_common.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_common.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_common_loader.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_common_loader.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_common_loader.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_common_loader.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_clock_init.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_clock_init.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_clock_init.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_clock_init.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_mem.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_mem.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_mem.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_mem.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_random.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_random.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_random.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_random.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_random_esp32c6.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_random_esp32c6.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_random_esp32c6.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_random_esp32c6.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_efuse.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_efuse.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_efuse.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_efuse.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/flash_encrypt.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/flash_encrypt.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/flash_encrypt.c > CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/flash_encrypt.c -o CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/secure_boot.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/secure_boot.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/secure_boot.c > CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/secure_boot.c -o CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/bootloader_flash.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/bootloader_flash.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/bootloader_flash.c > CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/bootloader_flash.c -o CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c > CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c -o CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32c6.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32c6.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32c6.c > CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32c6.c -o CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_utility.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_utility.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_utility.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_utility.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/flash_partitions.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/flash_partitions.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/flash_partitions.c > CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/flash_partitions.c -o CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp_image_format.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp_image_format.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp_image_format.c > CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp_image_format.c -o CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_init.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_init.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_init.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_init.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_clock_loader.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_clock_loader.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_clock_loader.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_clock_loader.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_console.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_console.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_console.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_console.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_console_loader.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_console_loader.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_console_loader.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_console_loader.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_sha.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_sha.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_sha.c > CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_sha.c -o CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_soc.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_soc.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_soc.c > CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_soc.c -o CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_esp32c6.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_esp32c6.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_esp32c6.c > CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_esp32c6.c -o CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_ecdsa.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_ecdsa.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_ecdsa.c > CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/esp32c6/bootloader_ecdsa.c -o CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.s

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/flags.make
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj: /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_panic.c
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj -MF CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj.d -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj -c /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_panic.c

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.i"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_panic.c > CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.i

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.s"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/esp-idf-anviz/components/bootloader_support/src/bootloader_panic.c -o CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.s

# Object files for target __idf_bootloader_support
__idf_bootloader_support_OBJECTS = \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.obj" \
"CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj"

# External object files for target __idf_bootloader_support
__idf_bootloader_support_EXTERNAL_OBJECTS =

esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make
esp-idf/bootloader_support/libbootloader_support.a: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Linking C static library libbootloader_support.a"
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && $(CMAKE_COMMAND) -P CMakeFiles/__idf_bootloader_support.dir/cmake_clean_target.cmake
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/__idf_bootloader_support.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build: esp-idf/bootloader_support/libbootloader_support.a
.PHONY : esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/clean:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support && $(CMAKE_COMMAND) -P CMakeFiles/__idf_bootloader_support.dir/cmake_clean.cmake
.PHONY : esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/clean

esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/depend:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/esp-idf-anviz/components/bootloader/subproject /home/<USER>/esp-idf-anviz/components/bootloader_support /home/<USER>/test/coap/examples/coap_client/build/bootloader /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/depend

