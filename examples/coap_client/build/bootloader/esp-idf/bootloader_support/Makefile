# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/esp-idf-anviz/components/bootloader/subproject

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test/coap/examples/coap_client/build/bootloader

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/bootloader_support//CMakeFiles/progress.marks
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/bootloader_support/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/bootloader_support/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/bootloader_support/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/bootloader_support/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/rule:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/rule
.PHONY : esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/rule

# Convenience name for target.
__idf_bootloader_support: esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/rule
.PHONY : __idf_bootloader_support

# fast build rule for target.
__idf_bootloader_support/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build
.PHONY : __idf_bootloader_support/fast

bootloader_flash/src/bootloader_flash.obj: bootloader_flash/src/bootloader_flash.c.obj
.PHONY : bootloader_flash/src/bootloader_flash.obj

# target to build an object file
bootloader_flash/src/bootloader_flash.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj
.PHONY : bootloader_flash/src/bootloader_flash.c.obj

bootloader_flash/src/bootloader_flash.i: bootloader_flash/src/bootloader_flash.c.i
.PHONY : bootloader_flash/src/bootloader_flash.i

# target to preprocess a source file
bootloader_flash/src/bootloader_flash.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.i
.PHONY : bootloader_flash/src/bootloader_flash.c.i

bootloader_flash/src/bootloader_flash.s: bootloader_flash/src/bootloader_flash.c.s
.PHONY : bootloader_flash/src/bootloader_flash.s

# target to generate assembly for a file
bootloader_flash/src/bootloader_flash.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.s
.PHONY : bootloader_flash/src/bootloader_flash.c.s

bootloader_flash/src/bootloader_flash_config_esp32c6.obj: bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj
.PHONY : bootloader_flash/src/bootloader_flash_config_esp32c6.obj

# target to build an object file
bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj
.PHONY : bootloader_flash/src/bootloader_flash_config_esp32c6.c.obj

bootloader_flash/src/bootloader_flash_config_esp32c6.i: bootloader_flash/src/bootloader_flash_config_esp32c6.c.i
.PHONY : bootloader_flash/src/bootloader_flash_config_esp32c6.i

# target to preprocess a source file
bootloader_flash/src/bootloader_flash_config_esp32c6.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.i
.PHONY : bootloader_flash/src/bootloader_flash_config_esp32c6.c.i

bootloader_flash/src/bootloader_flash_config_esp32c6.s: bootloader_flash/src/bootloader_flash_config_esp32c6.c.s
.PHONY : bootloader_flash/src/bootloader_flash_config_esp32c6.s

# target to generate assembly for a file
bootloader_flash/src/bootloader_flash_config_esp32c6.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c6.c.s
.PHONY : bootloader_flash/src/bootloader_flash_config_esp32c6.c.s

bootloader_flash/src/flash_qio_mode.obj: bootloader_flash/src/flash_qio_mode.c.obj
.PHONY : bootloader_flash/src/flash_qio_mode.obj

# target to build an object file
bootloader_flash/src/flash_qio_mode.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj
.PHONY : bootloader_flash/src/flash_qio_mode.c.obj

bootloader_flash/src/flash_qio_mode.i: bootloader_flash/src/flash_qio_mode.c.i
.PHONY : bootloader_flash/src/flash_qio_mode.i

# target to preprocess a source file
bootloader_flash/src/flash_qio_mode.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.i
.PHONY : bootloader_flash/src/flash_qio_mode.c.i

bootloader_flash/src/flash_qio_mode.s: bootloader_flash/src/flash_qio_mode.c.s
.PHONY : bootloader_flash/src/flash_qio_mode.s

# target to generate assembly for a file
bootloader_flash/src/flash_qio_mode.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.s
.PHONY : bootloader_flash/src/flash_qio_mode.c.s

src/bootloader_clock_init.obj: src/bootloader_clock_init.c.obj
.PHONY : src/bootloader_clock_init.obj

# target to build an object file
src/bootloader_clock_init.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj
.PHONY : src/bootloader_clock_init.c.obj

src/bootloader_clock_init.i: src/bootloader_clock_init.c.i
.PHONY : src/bootloader_clock_init.i

# target to preprocess a source file
src/bootloader_clock_init.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.i
.PHONY : src/bootloader_clock_init.c.i

src/bootloader_clock_init.s: src/bootloader_clock_init.c.s
.PHONY : src/bootloader_clock_init.s

# target to generate assembly for a file
src/bootloader_clock_init.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.s
.PHONY : src/bootloader_clock_init.c.s

src/bootloader_clock_loader.obj: src/bootloader_clock_loader.c.obj
.PHONY : src/bootloader_clock_loader.obj

# target to build an object file
src/bootloader_clock_loader.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj
.PHONY : src/bootloader_clock_loader.c.obj

src/bootloader_clock_loader.i: src/bootloader_clock_loader.c.i
.PHONY : src/bootloader_clock_loader.i

# target to preprocess a source file
src/bootloader_clock_loader.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.i
.PHONY : src/bootloader_clock_loader.c.i

src/bootloader_clock_loader.s: src/bootloader_clock_loader.c.s
.PHONY : src/bootloader_clock_loader.s

# target to generate assembly for a file
src/bootloader_clock_loader.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.s
.PHONY : src/bootloader_clock_loader.c.s

src/bootloader_common.obj: src/bootloader_common.c.obj
.PHONY : src/bootloader_common.obj

# target to build an object file
src/bootloader_common.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj
.PHONY : src/bootloader_common.c.obj

src/bootloader_common.i: src/bootloader_common.c.i
.PHONY : src/bootloader_common.i

# target to preprocess a source file
src/bootloader_common.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.i
.PHONY : src/bootloader_common.c.i

src/bootloader_common.s: src/bootloader_common.c.s
.PHONY : src/bootloader_common.s

# target to generate assembly for a file
src/bootloader_common.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.s
.PHONY : src/bootloader_common.c.s

src/bootloader_common_loader.obj: src/bootloader_common_loader.c.obj
.PHONY : src/bootloader_common_loader.obj

# target to build an object file
src/bootloader_common_loader.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj
.PHONY : src/bootloader_common_loader.c.obj

src/bootloader_common_loader.i: src/bootloader_common_loader.c.i
.PHONY : src/bootloader_common_loader.i

# target to preprocess a source file
src/bootloader_common_loader.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.i
.PHONY : src/bootloader_common_loader.c.i

src/bootloader_common_loader.s: src/bootloader_common_loader.c.s
.PHONY : src/bootloader_common_loader.s

# target to generate assembly for a file
src/bootloader_common_loader.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.s
.PHONY : src/bootloader_common_loader.c.s

src/bootloader_console.obj: src/bootloader_console.c.obj
.PHONY : src/bootloader_console.obj

# target to build an object file
src/bootloader_console.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj
.PHONY : src/bootloader_console.c.obj

src/bootloader_console.i: src/bootloader_console.c.i
.PHONY : src/bootloader_console.i

# target to preprocess a source file
src/bootloader_console.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.i
.PHONY : src/bootloader_console.c.i

src/bootloader_console.s: src/bootloader_console.c.s
.PHONY : src/bootloader_console.s

# target to generate assembly for a file
src/bootloader_console.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.s
.PHONY : src/bootloader_console.c.s

src/bootloader_console_loader.obj: src/bootloader_console_loader.c.obj
.PHONY : src/bootloader_console_loader.obj

# target to build an object file
src/bootloader_console_loader.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj
.PHONY : src/bootloader_console_loader.c.obj

src/bootloader_console_loader.i: src/bootloader_console_loader.c.i
.PHONY : src/bootloader_console_loader.i

# target to preprocess a source file
src/bootloader_console_loader.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.i
.PHONY : src/bootloader_console_loader.c.i

src/bootloader_console_loader.s: src/bootloader_console_loader.c.s
.PHONY : src/bootloader_console_loader.s

# target to generate assembly for a file
src/bootloader_console_loader.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.s
.PHONY : src/bootloader_console_loader.c.s

src/bootloader_efuse.obj: src/bootloader_efuse.c.obj
.PHONY : src/bootloader_efuse.obj

# target to build an object file
src/bootloader_efuse.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj
.PHONY : src/bootloader_efuse.c.obj

src/bootloader_efuse.i: src/bootloader_efuse.c.i
.PHONY : src/bootloader_efuse.i

# target to preprocess a source file
src/bootloader_efuse.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.i
.PHONY : src/bootloader_efuse.c.i

src/bootloader_efuse.s: src/bootloader_efuse.c.s
.PHONY : src/bootloader_efuse.s

# target to generate assembly for a file
src/bootloader_efuse.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.s
.PHONY : src/bootloader_efuse.c.s

src/bootloader_init.obj: src/bootloader_init.c.obj
.PHONY : src/bootloader_init.obj

# target to build an object file
src/bootloader_init.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj
.PHONY : src/bootloader_init.c.obj

src/bootloader_init.i: src/bootloader_init.c.i
.PHONY : src/bootloader_init.i

# target to preprocess a source file
src/bootloader_init.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.i
.PHONY : src/bootloader_init.c.i

src/bootloader_init.s: src/bootloader_init.c.s
.PHONY : src/bootloader_init.s

# target to generate assembly for a file
src/bootloader_init.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.s
.PHONY : src/bootloader_init.c.s

src/bootloader_mem.obj: src/bootloader_mem.c.obj
.PHONY : src/bootloader_mem.obj

# target to build an object file
src/bootloader_mem.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj
.PHONY : src/bootloader_mem.c.obj

src/bootloader_mem.i: src/bootloader_mem.c.i
.PHONY : src/bootloader_mem.i

# target to preprocess a source file
src/bootloader_mem.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.i
.PHONY : src/bootloader_mem.c.i

src/bootloader_mem.s: src/bootloader_mem.c.s
.PHONY : src/bootloader_mem.s

# target to generate assembly for a file
src/bootloader_mem.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.s
.PHONY : src/bootloader_mem.c.s

src/bootloader_panic.obj: src/bootloader_panic.c.obj
.PHONY : src/bootloader_panic.obj

# target to build an object file
src/bootloader_panic.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj
.PHONY : src/bootloader_panic.c.obj

src/bootloader_panic.i: src/bootloader_panic.c.i
.PHONY : src/bootloader_panic.i

# target to preprocess a source file
src/bootloader_panic.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.i
.PHONY : src/bootloader_panic.c.i

src/bootloader_panic.s: src/bootloader_panic.c.s
.PHONY : src/bootloader_panic.s

# target to generate assembly for a file
src/bootloader_panic.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.s
.PHONY : src/bootloader_panic.c.s

src/bootloader_random.obj: src/bootloader_random.c.obj
.PHONY : src/bootloader_random.obj

# target to build an object file
src/bootloader_random.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj
.PHONY : src/bootloader_random.c.obj

src/bootloader_random.i: src/bootloader_random.c.i
.PHONY : src/bootloader_random.i

# target to preprocess a source file
src/bootloader_random.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.i
.PHONY : src/bootloader_random.c.i

src/bootloader_random.s: src/bootloader_random.c.s
.PHONY : src/bootloader_random.s

# target to generate assembly for a file
src/bootloader_random.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.s
.PHONY : src/bootloader_random.c.s

src/bootloader_random_esp32c6.obj: src/bootloader_random_esp32c6.c.obj
.PHONY : src/bootloader_random_esp32c6.obj

# target to build an object file
src/bootloader_random_esp32c6.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.obj
.PHONY : src/bootloader_random_esp32c6.c.obj

src/bootloader_random_esp32c6.i: src/bootloader_random_esp32c6.c.i
.PHONY : src/bootloader_random_esp32c6.i

# target to preprocess a source file
src/bootloader_random_esp32c6.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.i
.PHONY : src/bootloader_random_esp32c6.c.i

src/bootloader_random_esp32c6.s: src/bootloader_random_esp32c6.c.s
.PHONY : src/bootloader_random_esp32c6.s

# target to generate assembly for a file
src/bootloader_random_esp32c6.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c6.c.s
.PHONY : src/bootloader_random_esp32c6.c.s

src/bootloader_utility.obj: src/bootloader_utility.c.obj
.PHONY : src/bootloader_utility.obj

# target to build an object file
src/bootloader_utility.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj
.PHONY : src/bootloader_utility.c.obj

src/bootloader_utility.i: src/bootloader_utility.c.i
.PHONY : src/bootloader_utility.i

# target to preprocess a source file
src/bootloader_utility.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.i
.PHONY : src/bootloader_utility.c.i

src/bootloader_utility.s: src/bootloader_utility.c.s
.PHONY : src/bootloader_utility.s

# target to generate assembly for a file
src/bootloader_utility.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.s
.PHONY : src/bootloader_utility.c.s

src/esp32c6/bootloader_ecdsa.obj: src/esp32c6/bootloader_ecdsa.c.obj
.PHONY : src/esp32c6/bootloader_ecdsa.obj

# target to build an object file
src/esp32c6/bootloader_ecdsa.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.obj
.PHONY : src/esp32c6/bootloader_ecdsa.c.obj

src/esp32c6/bootloader_ecdsa.i: src/esp32c6/bootloader_ecdsa.c.i
.PHONY : src/esp32c6/bootloader_ecdsa.i

# target to preprocess a source file
src/esp32c6/bootloader_ecdsa.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.i
.PHONY : src/esp32c6/bootloader_ecdsa.c.i

src/esp32c6/bootloader_ecdsa.s: src/esp32c6/bootloader_ecdsa.c.s
.PHONY : src/esp32c6/bootloader_ecdsa.s

# target to generate assembly for a file
src/esp32c6/bootloader_ecdsa.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_ecdsa.c.s
.PHONY : src/esp32c6/bootloader_ecdsa.c.s

src/esp32c6/bootloader_esp32c6.obj: src/esp32c6/bootloader_esp32c6.c.obj
.PHONY : src/esp32c6/bootloader_esp32c6.obj

# target to build an object file
src/esp32c6/bootloader_esp32c6.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.obj
.PHONY : src/esp32c6/bootloader_esp32c6.c.obj

src/esp32c6/bootloader_esp32c6.i: src/esp32c6/bootloader_esp32c6.c.i
.PHONY : src/esp32c6/bootloader_esp32c6.i

# target to preprocess a source file
src/esp32c6/bootloader_esp32c6.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.i
.PHONY : src/esp32c6/bootloader_esp32c6.c.i

src/esp32c6/bootloader_esp32c6.s: src/esp32c6/bootloader_esp32c6.c.s
.PHONY : src/esp32c6/bootloader_esp32c6.s

# target to generate assembly for a file
src/esp32c6/bootloader_esp32c6.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_esp32c6.c.s
.PHONY : src/esp32c6/bootloader_esp32c6.c.s

src/esp32c6/bootloader_sha.obj: src/esp32c6/bootloader_sha.c.obj
.PHONY : src/esp32c6/bootloader_sha.obj

# target to build an object file
src/esp32c6/bootloader_sha.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.obj
.PHONY : src/esp32c6/bootloader_sha.c.obj

src/esp32c6/bootloader_sha.i: src/esp32c6/bootloader_sha.c.i
.PHONY : src/esp32c6/bootloader_sha.i

# target to preprocess a source file
src/esp32c6/bootloader_sha.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.i
.PHONY : src/esp32c6/bootloader_sha.c.i

src/esp32c6/bootloader_sha.s: src/esp32c6/bootloader_sha.c.s
.PHONY : src/esp32c6/bootloader_sha.s

# target to generate assembly for a file
src/esp32c6/bootloader_sha.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_sha.c.s
.PHONY : src/esp32c6/bootloader_sha.c.s

src/esp32c6/bootloader_soc.obj: src/esp32c6/bootloader_soc.c.obj
.PHONY : src/esp32c6/bootloader_soc.obj

# target to build an object file
src/esp32c6/bootloader_soc.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.obj
.PHONY : src/esp32c6/bootloader_soc.c.obj

src/esp32c6/bootloader_soc.i: src/esp32c6/bootloader_soc.c.i
.PHONY : src/esp32c6/bootloader_soc.i

# target to preprocess a source file
src/esp32c6/bootloader_soc.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.i
.PHONY : src/esp32c6/bootloader_soc.c.i

src/esp32c6/bootloader_soc.s: src/esp32c6/bootloader_soc.c.s
.PHONY : src/esp32c6/bootloader_soc.s

# target to generate assembly for a file
src/esp32c6/bootloader_soc.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c6/bootloader_soc.c.s
.PHONY : src/esp32c6/bootloader_soc.c.s

src/esp_image_format.obj: src/esp_image_format.c.obj
.PHONY : src/esp_image_format.obj

# target to build an object file
src/esp_image_format.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj
.PHONY : src/esp_image_format.c.obj

src/esp_image_format.i: src/esp_image_format.c.i
.PHONY : src/esp_image_format.i

# target to preprocess a source file
src/esp_image_format.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.i
.PHONY : src/esp_image_format.c.i

src/esp_image_format.s: src/esp_image_format.c.s
.PHONY : src/esp_image_format.s

# target to generate assembly for a file
src/esp_image_format.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.s
.PHONY : src/esp_image_format.c.s

src/flash_encrypt.obj: src/flash_encrypt.c.obj
.PHONY : src/flash_encrypt.obj

# target to build an object file
src/flash_encrypt.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj
.PHONY : src/flash_encrypt.c.obj

src/flash_encrypt.i: src/flash_encrypt.c.i
.PHONY : src/flash_encrypt.i

# target to preprocess a source file
src/flash_encrypt.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.i
.PHONY : src/flash_encrypt.c.i

src/flash_encrypt.s: src/flash_encrypt.c.s
.PHONY : src/flash_encrypt.s

# target to generate assembly for a file
src/flash_encrypt.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.s
.PHONY : src/flash_encrypt.c.s

src/flash_partitions.obj: src/flash_partitions.c.obj
.PHONY : src/flash_partitions.obj

# target to build an object file
src/flash_partitions.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj
.PHONY : src/flash_partitions.c.obj

src/flash_partitions.i: src/flash_partitions.c.i
.PHONY : src/flash_partitions.i

# target to preprocess a source file
src/flash_partitions.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.i
.PHONY : src/flash_partitions.c.i

src/flash_partitions.s: src/flash_partitions.c.s
.PHONY : src/flash_partitions.s

# target to generate assembly for a file
src/flash_partitions.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.s
.PHONY : src/flash_partitions.c.s

src/secure_boot.obj: src/secure_boot.c.obj
.PHONY : src/secure_boot.obj

# target to build an object file
src/secure_boot.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj
.PHONY : src/secure_boot.c.obj

src/secure_boot.i: src/secure_boot.c.i
.PHONY : src/secure_boot.i

# target to preprocess a source file
src/secure_boot.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.i
.PHONY : src/secure_boot.c.i

src/secure_boot.s: src/secure_boot.c.s
.PHONY : src/secure_boot.s

# target to generate assembly for a file
src/secure_boot.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/build.make esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.s
.PHONY : src/secure_boot.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... __idf_bootloader_support"
	@echo "... bootloader_flash/src/bootloader_flash.obj"
	@echo "... bootloader_flash/src/bootloader_flash.i"
	@echo "... bootloader_flash/src/bootloader_flash.s"
	@echo "... bootloader_flash/src/bootloader_flash_config_esp32c6.obj"
	@echo "... bootloader_flash/src/bootloader_flash_config_esp32c6.i"
	@echo "... bootloader_flash/src/bootloader_flash_config_esp32c6.s"
	@echo "... bootloader_flash/src/flash_qio_mode.obj"
	@echo "... bootloader_flash/src/flash_qio_mode.i"
	@echo "... bootloader_flash/src/flash_qio_mode.s"
	@echo "... src/bootloader_clock_init.obj"
	@echo "... src/bootloader_clock_init.i"
	@echo "... src/bootloader_clock_init.s"
	@echo "... src/bootloader_clock_loader.obj"
	@echo "... src/bootloader_clock_loader.i"
	@echo "... src/bootloader_clock_loader.s"
	@echo "... src/bootloader_common.obj"
	@echo "... src/bootloader_common.i"
	@echo "... src/bootloader_common.s"
	@echo "... src/bootloader_common_loader.obj"
	@echo "... src/bootloader_common_loader.i"
	@echo "... src/bootloader_common_loader.s"
	@echo "... src/bootloader_console.obj"
	@echo "... src/bootloader_console.i"
	@echo "... src/bootloader_console.s"
	@echo "... src/bootloader_console_loader.obj"
	@echo "... src/bootloader_console_loader.i"
	@echo "... src/bootloader_console_loader.s"
	@echo "... src/bootloader_efuse.obj"
	@echo "... src/bootloader_efuse.i"
	@echo "... src/bootloader_efuse.s"
	@echo "... src/bootloader_init.obj"
	@echo "... src/bootloader_init.i"
	@echo "... src/bootloader_init.s"
	@echo "... src/bootloader_mem.obj"
	@echo "... src/bootloader_mem.i"
	@echo "... src/bootloader_mem.s"
	@echo "... src/bootloader_panic.obj"
	@echo "... src/bootloader_panic.i"
	@echo "... src/bootloader_panic.s"
	@echo "... src/bootloader_random.obj"
	@echo "... src/bootloader_random.i"
	@echo "... src/bootloader_random.s"
	@echo "... src/bootloader_random_esp32c6.obj"
	@echo "... src/bootloader_random_esp32c6.i"
	@echo "... src/bootloader_random_esp32c6.s"
	@echo "... src/bootloader_utility.obj"
	@echo "... src/bootloader_utility.i"
	@echo "... src/bootloader_utility.s"
	@echo "... src/esp32c6/bootloader_ecdsa.obj"
	@echo "... src/esp32c6/bootloader_ecdsa.i"
	@echo "... src/esp32c6/bootloader_ecdsa.s"
	@echo "... src/esp32c6/bootloader_esp32c6.obj"
	@echo "... src/esp32c6/bootloader_esp32c6.i"
	@echo "... src/esp32c6/bootloader_esp32c6.s"
	@echo "... src/esp32c6/bootloader_sha.obj"
	@echo "... src/esp32c6/bootloader_sha.i"
	@echo "... src/esp32c6/bootloader_sha.s"
	@echo "... src/esp32c6/bootloader_soc.obj"
	@echo "... src/esp32c6/bootloader_soc.i"
	@echo "... src/esp32c6/bootloader_soc.s"
	@echo "... src/esp_image_format.obj"
	@echo "... src/esp_image_format.i"
	@echo "... src/esp_image_format.s"
	@echo "... src/flash_encrypt.obj"
	@echo "... src/flash_encrypt.i"
	@echo "... src/flash_encrypt.s"
	@echo "... src/flash_partitions.obj"
	@echo "... src/flash_partitions.i"
	@echo "... src/flash_partitions.s"
	@echo "... src/secure_boot.obj"
	@echo "... src/secure_boot.i"
	@echo "... src/secure_boot.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

