file(REMOVE_RECURSE
  "CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj"
  "CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj.d"
  "CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj"
  "CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj.d"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/chip_info.c.obj"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/chip_info.c.obj.d"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/cpu_region_protect.c.obj"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/cpu_region_protect.c.obj.d"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/ocode_init.c.obj"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/ocode_init.c.obj.d"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_init.c.obj"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_init.c.obj.d"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_param.c.obj"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_param.c.obj.d"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_sleep.c.obj"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_sleep.c.obj.d"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_clk.c.obj"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_clk.c.obj.d"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_clk_init.c.obj"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_clk_init.c.obj.d"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_time.c.obj"
  "CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_time.c.obj.d"
  "libesp_hw_support.a"
  "libesp_hw_support.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/__idf_esp_hw_support.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
