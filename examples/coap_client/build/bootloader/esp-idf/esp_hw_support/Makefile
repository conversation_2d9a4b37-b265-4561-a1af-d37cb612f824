# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/esp-idf-anviz/components/bootloader/subproject

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test/coap/examples/coap_client/build/bootloader

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/esp_hw_support//CMakeFiles/progress.marks
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_hw_support/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_hw_support/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_hw_support/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_hw_support/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rule:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rule
.PHONY : esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rule

# Convenience name for target.
__idf_esp_hw_support: esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rule
.PHONY : __idf_esp_hw_support

# fast build rule for target.
__idf_esp_hw_support/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build
.PHONY : __idf_esp_hw_support/fast

cpu.obj: cpu.c.obj
.PHONY : cpu.obj

# target to build an object file
cpu.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj
.PHONY : cpu.c.obj

cpu.i: cpu.c.i
.PHONY : cpu.i

# target to preprocess a source file
cpu.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.i
.PHONY : cpu.c.i

cpu.s: cpu.c.s
.PHONY : cpu.s

# target to generate assembly for a file
cpu.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.s
.PHONY : cpu.c.s

esp_memory_utils.obj: esp_memory_utils.c.obj
.PHONY : esp_memory_utils.obj

# target to build an object file
esp_memory_utils.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj
.PHONY : esp_memory_utils.c.obj

esp_memory_utils.i: esp_memory_utils.c.i
.PHONY : esp_memory_utils.i

# target to preprocess a source file
esp_memory_utils.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.i
.PHONY : esp_memory_utils.c.i

esp_memory_utils.s: esp_memory_utils.c.s
.PHONY : esp_memory_utils.s

# target to generate assembly for a file
esp_memory_utils.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.s
.PHONY : esp_memory_utils.c.s

port/esp32c6/chip_info.obj: port/esp32c6/chip_info.c.obj
.PHONY : port/esp32c6/chip_info.obj

# target to build an object file
port/esp32c6/chip_info.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/chip_info.c.obj
.PHONY : port/esp32c6/chip_info.c.obj

port/esp32c6/chip_info.i: port/esp32c6/chip_info.c.i
.PHONY : port/esp32c6/chip_info.i

# target to preprocess a source file
port/esp32c6/chip_info.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/chip_info.c.i
.PHONY : port/esp32c6/chip_info.c.i

port/esp32c6/chip_info.s: port/esp32c6/chip_info.c.s
.PHONY : port/esp32c6/chip_info.s

# target to generate assembly for a file
port/esp32c6/chip_info.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/chip_info.c.s
.PHONY : port/esp32c6/chip_info.c.s

port/esp32c6/cpu_region_protect.obj: port/esp32c6/cpu_region_protect.c.obj
.PHONY : port/esp32c6/cpu_region_protect.obj

# target to build an object file
port/esp32c6/cpu_region_protect.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/cpu_region_protect.c.obj
.PHONY : port/esp32c6/cpu_region_protect.c.obj

port/esp32c6/cpu_region_protect.i: port/esp32c6/cpu_region_protect.c.i
.PHONY : port/esp32c6/cpu_region_protect.i

# target to preprocess a source file
port/esp32c6/cpu_region_protect.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/cpu_region_protect.c.i
.PHONY : port/esp32c6/cpu_region_protect.c.i

port/esp32c6/cpu_region_protect.s: port/esp32c6/cpu_region_protect.c.s
.PHONY : port/esp32c6/cpu_region_protect.s

# target to generate assembly for a file
port/esp32c6/cpu_region_protect.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/cpu_region_protect.c.s
.PHONY : port/esp32c6/cpu_region_protect.c.s

port/esp32c6/ocode_init.obj: port/esp32c6/ocode_init.c.obj
.PHONY : port/esp32c6/ocode_init.obj

# target to build an object file
port/esp32c6/ocode_init.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/ocode_init.c.obj
.PHONY : port/esp32c6/ocode_init.c.obj

port/esp32c6/ocode_init.i: port/esp32c6/ocode_init.c.i
.PHONY : port/esp32c6/ocode_init.i

# target to preprocess a source file
port/esp32c6/ocode_init.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/ocode_init.c.i
.PHONY : port/esp32c6/ocode_init.c.i

port/esp32c6/ocode_init.s: port/esp32c6/ocode_init.c.s
.PHONY : port/esp32c6/ocode_init.s

# target to generate assembly for a file
port/esp32c6/ocode_init.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/ocode_init.c.s
.PHONY : port/esp32c6/ocode_init.c.s

port/esp32c6/pmu_init.obj: port/esp32c6/pmu_init.c.obj
.PHONY : port/esp32c6/pmu_init.obj

# target to build an object file
port/esp32c6/pmu_init.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_init.c.obj
.PHONY : port/esp32c6/pmu_init.c.obj

port/esp32c6/pmu_init.i: port/esp32c6/pmu_init.c.i
.PHONY : port/esp32c6/pmu_init.i

# target to preprocess a source file
port/esp32c6/pmu_init.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_init.c.i
.PHONY : port/esp32c6/pmu_init.c.i

port/esp32c6/pmu_init.s: port/esp32c6/pmu_init.c.s
.PHONY : port/esp32c6/pmu_init.s

# target to generate assembly for a file
port/esp32c6/pmu_init.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_init.c.s
.PHONY : port/esp32c6/pmu_init.c.s

port/esp32c6/pmu_param.obj: port/esp32c6/pmu_param.c.obj
.PHONY : port/esp32c6/pmu_param.obj

# target to build an object file
port/esp32c6/pmu_param.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_param.c.obj
.PHONY : port/esp32c6/pmu_param.c.obj

port/esp32c6/pmu_param.i: port/esp32c6/pmu_param.c.i
.PHONY : port/esp32c6/pmu_param.i

# target to preprocess a source file
port/esp32c6/pmu_param.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_param.c.i
.PHONY : port/esp32c6/pmu_param.c.i

port/esp32c6/pmu_param.s: port/esp32c6/pmu_param.c.s
.PHONY : port/esp32c6/pmu_param.s

# target to generate assembly for a file
port/esp32c6/pmu_param.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_param.c.s
.PHONY : port/esp32c6/pmu_param.c.s

port/esp32c6/pmu_sleep.obj: port/esp32c6/pmu_sleep.c.obj
.PHONY : port/esp32c6/pmu_sleep.obj

# target to build an object file
port/esp32c6/pmu_sleep.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_sleep.c.obj
.PHONY : port/esp32c6/pmu_sleep.c.obj

port/esp32c6/pmu_sleep.i: port/esp32c6/pmu_sleep.c.i
.PHONY : port/esp32c6/pmu_sleep.i

# target to preprocess a source file
port/esp32c6/pmu_sleep.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_sleep.c.i
.PHONY : port/esp32c6/pmu_sleep.c.i

port/esp32c6/pmu_sleep.s: port/esp32c6/pmu_sleep.c.s
.PHONY : port/esp32c6/pmu_sleep.s

# target to generate assembly for a file
port/esp32c6/pmu_sleep.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/pmu_sleep.c.s
.PHONY : port/esp32c6/pmu_sleep.c.s

port/esp32c6/rtc_clk.obj: port/esp32c6/rtc_clk.c.obj
.PHONY : port/esp32c6/rtc_clk.obj

# target to build an object file
port/esp32c6/rtc_clk.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_clk.c.obj
.PHONY : port/esp32c6/rtc_clk.c.obj

port/esp32c6/rtc_clk.i: port/esp32c6/rtc_clk.c.i
.PHONY : port/esp32c6/rtc_clk.i

# target to preprocess a source file
port/esp32c6/rtc_clk.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_clk.c.i
.PHONY : port/esp32c6/rtc_clk.c.i

port/esp32c6/rtc_clk.s: port/esp32c6/rtc_clk.c.s
.PHONY : port/esp32c6/rtc_clk.s

# target to generate assembly for a file
port/esp32c6/rtc_clk.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_clk.c.s
.PHONY : port/esp32c6/rtc_clk.c.s

port/esp32c6/rtc_clk_init.obj: port/esp32c6/rtc_clk_init.c.obj
.PHONY : port/esp32c6/rtc_clk_init.obj

# target to build an object file
port/esp32c6/rtc_clk_init.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_clk_init.c.obj
.PHONY : port/esp32c6/rtc_clk_init.c.obj

port/esp32c6/rtc_clk_init.i: port/esp32c6/rtc_clk_init.c.i
.PHONY : port/esp32c6/rtc_clk_init.i

# target to preprocess a source file
port/esp32c6/rtc_clk_init.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_clk_init.c.i
.PHONY : port/esp32c6/rtc_clk_init.c.i

port/esp32c6/rtc_clk_init.s: port/esp32c6/rtc_clk_init.c.s
.PHONY : port/esp32c6/rtc_clk_init.s

# target to generate assembly for a file
port/esp32c6/rtc_clk_init.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_clk_init.c.s
.PHONY : port/esp32c6/rtc_clk_init.c.s

port/esp32c6/rtc_time.obj: port/esp32c6/rtc_time.c.obj
.PHONY : port/esp32c6/rtc_time.obj

# target to build an object file
port/esp32c6/rtc_time.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_time.c.obj
.PHONY : port/esp32c6/rtc_time.c.obj

port/esp32c6/rtc_time.i: port/esp32c6/rtc_time.c.i
.PHONY : port/esp32c6/rtc_time.i

# target to preprocess a source file
port/esp32c6/rtc_time.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_time.c.i
.PHONY : port/esp32c6/rtc_time.c.i

port/esp32c6/rtc_time.s: port/esp32c6/rtc_time.c.s
.PHONY : port/esp32c6/rtc_time.s

# target to generate assembly for a file
port/esp32c6/rtc_time.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/build.make esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c6/rtc_time.c.s
.PHONY : port/esp32c6/rtc_time.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... __idf_esp_hw_support"
	@echo "... cpu.obj"
	@echo "... cpu.i"
	@echo "... cpu.s"
	@echo "... esp_memory_utils.obj"
	@echo "... esp_memory_utils.i"
	@echo "... esp_memory_utils.s"
	@echo "... port/esp32c6/chip_info.obj"
	@echo "... port/esp32c6/chip_info.i"
	@echo "... port/esp32c6/chip_info.s"
	@echo "... port/esp32c6/cpu_region_protect.obj"
	@echo "... port/esp32c6/cpu_region_protect.i"
	@echo "... port/esp32c6/cpu_region_protect.s"
	@echo "... port/esp32c6/ocode_init.obj"
	@echo "... port/esp32c6/ocode_init.i"
	@echo "... port/esp32c6/ocode_init.s"
	@echo "... port/esp32c6/pmu_init.obj"
	@echo "... port/esp32c6/pmu_init.i"
	@echo "... port/esp32c6/pmu_init.s"
	@echo "... port/esp32c6/pmu_param.obj"
	@echo "... port/esp32c6/pmu_param.i"
	@echo "... port/esp32c6/pmu_param.s"
	@echo "... port/esp32c6/pmu_sleep.obj"
	@echo "... port/esp32c6/pmu_sleep.i"
	@echo "... port/esp32c6/pmu_sleep.s"
	@echo "... port/esp32c6/rtc_clk.obj"
	@echo "... port/esp32c6/rtc_clk.i"
	@echo "... port/esp32c6/rtc_clk.s"
	@echo "... port/esp32c6/rtc_clk_init.obj"
	@echo "... port/esp32c6/rtc_clk_init.i"
	@echo "... port/esp32c6/rtc_clk_init.s"
	@echo "... port/esp32c6/rtc_time.obj"
	@echo "... port/esp32c6/rtc_time.i"
	@echo "... port/esp32c6/rtc_time.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

