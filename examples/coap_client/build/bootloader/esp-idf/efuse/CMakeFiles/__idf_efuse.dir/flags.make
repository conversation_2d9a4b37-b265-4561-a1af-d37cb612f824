# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile C with /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc
C_DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"6d94249f84-dirty\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE

C_INCLUDES = -I/home/<USER>/test/coap/examples/coap_client/build/bootloader/config -I/home/<USER>/esp-idf-anviz/components/efuse/include -I/home/<USER>/esp-idf-anviz/components/efuse/esp32c6/include -I/home/<USER>/esp-idf-anviz/components/efuse/private_include -I/home/<USER>/esp-idf-anviz/components/efuse/esp32c6/private_include -I/home/<USER>/esp-idf-anviz/components/log/include -I/home/<USER>/esp-idf-anviz/components/esp_rom/include -I/home/<USER>/esp-idf-anviz/components/esp_rom/include/esp32c6 -I/home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6 -I/home/<USER>/esp-idf-anviz/components/esp_common/include -I/home/<USER>/esp-idf-anviz/components/esp_hw_support/include -I/home/<USER>/esp-idf-anviz/components/esp_hw_support/include/soc -I/home/<USER>/esp-idf-anviz/components/esp_hw_support/include/soc/esp32c6 -I/home/<USER>/esp-idf-anviz/components/esp_hw_support/port/esp32c6/. -I/home/<USER>/esp-idf-anviz/components/esp_hw_support/port/esp32c6/private_include -I/home/<USER>/esp-idf-anviz/components/newlib/platform_include -I/home/<USER>/esp-idf-anviz/components/riscv/include -I/home/<USER>/esp-idf-anviz/components/soc/include -I/home/<USER>/esp-idf-anviz/components/soc/esp32c6 -I/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include -I/home/<USER>/esp-idf-anviz/components/bootloader_support/include -I/home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp-idf-anviz/components/bootloader_support/private_include -I/home/<USER>/esp-idf-anviz/components/spi_flash/include -I/home/<USER>/esp-idf-anviz/components/hal/esp32c6/include -I/home/<USER>/esp-idf-anviz/components/hal/include -I/home/<USER>/esp-idf-anviz/components/hal/platform_port/include

C_FLAGS = -march=rv32imac_zicsr_zifencei  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-error=format= -Wno-format -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=/home/<USER>/esp-idf-anviz/components/bootloader/subproject=. -fmacro-prefix-map=/home/<USER>/esp-idf-anviz=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration

