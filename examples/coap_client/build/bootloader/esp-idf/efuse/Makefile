# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/esp-idf-anviz/components/bootloader/subproject

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test/coap/examples/coap_client/build/bootloader

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles /home/<USER>/test/coap/examples/coap_client/build/bootloader/esp-idf/efuse//CMakeFiles/progress.marks
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/test/coap/examples/coap_client/build/bootloader/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
esp-idf/efuse/CMakeFiles/__idf_efuse.dir/rule:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/__idf_efuse.dir/rule
.PHONY : esp-idf/efuse/CMakeFiles/__idf_efuse.dir/rule

# Convenience name for target.
__idf_efuse: esp-idf/efuse/CMakeFiles/__idf_efuse.dir/rule
.PHONY : __idf_efuse

# fast build rule for target.
__idf_efuse/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build
.PHONY : __idf_efuse/fast

# Convenience name for target.
esp-idf/efuse/CMakeFiles/efuse-common-table.dir/rule:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse-common-table.dir/rule
.PHONY : esp-idf/efuse/CMakeFiles/efuse-common-table.dir/rule

# Convenience name for target.
efuse-common-table: esp-idf/efuse/CMakeFiles/efuse-common-table.dir/rule
.PHONY : efuse-common-table

# fast build rule for target.
efuse-common-table/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-common-table.dir/build
.PHONY : efuse-common-table/fast

# Convenience name for target.
esp-idf/efuse/CMakeFiles/efuse_common_table.dir/rule:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse_common_table.dir/rule
.PHONY : esp-idf/efuse/CMakeFiles/efuse_common_table.dir/rule

# Convenience name for target.
efuse_common_table: esp-idf/efuse/CMakeFiles/efuse_common_table.dir/rule
.PHONY : efuse_common_table

# fast build rule for target.
efuse_common_table/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_common_table.dir/build
.PHONY : efuse_common_table/fast

# Convenience name for target.
esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/rule:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/rule
.PHONY : esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/rule

# Convenience name for target.
efuse-custom-table: esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/rule
.PHONY : efuse-custom-table

# fast build rule for target.
efuse-custom-table/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build.make esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/build
.PHONY : efuse-custom-table/fast

# Convenience name for target.
esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/rule:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/rule
.PHONY : esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/rule

# Convenience name for target.
efuse_custom_table: esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/rule
.PHONY : efuse_custom_table

# fast build rule for target.
efuse_custom_table/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/build
.PHONY : efuse_custom_table/fast

# Convenience name for target.
esp-idf/efuse/CMakeFiles/show-efuse-table.dir/rule:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/show-efuse-table.dir/rule
.PHONY : esp-idf/efuse/CMakeFiles/show-efuse-table.dir/rule

# Convenience name for target.
show-efuse-table: esp-idf/efuse/CMakeFiles/show-efuse-table.dir/rule
.PHONY : show-efuse-table

# fast build rule for target.
show-efuse-table/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build.make esp-idf/efuse/CMakeFiles/show-efuse-table.dir/build
.PHONY : show-efuse-table/fast

# Convenience name for target.
esp-idf/efuse/CMakeFiles/show_efuse_table.dir/rule:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/show_efuse_table.dir/rule
.PHONY : esp-idf/efuse/CMakeFiles/show_efuse_table.dir/rule

# Convenience name for target.
show_efuse_table: esp-idf/efuse/CMakeFiles/show_efuse_table.dir/rule
.PHONY : show_efuse_table

# fast build rule for target.
show_efuse_table/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build.make esp-idf/efuse/CMakeFiles/show_efuse_table.dir/build
.PHONY : show_efuse_table/fast

# Convenience name for target.
esp-idf/efuse/CMakeFiles/efuse_test_table.dir/rule:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 esp-idf/efuse/CMakeFiles/efuse_test_table.dir/rule
.PHONY : esp-idf/efuse/CMakeFiles/efuse_test_table.dir/rule

# Convenience name for target.
efuse_test_table: esp-idf/efuse/CMakeFiles/efuse_test_table.dir/rule
.PHONY : efuse_test_table

# fast build rule for target.
efuse_test_table/fast:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build.make esp-idf/efuse/CMakeFiles/efuse_test_table.dir/build
.PHONY : efuse_test_table/fast

esp32c6/esp_efuse_fields.obj: esp32c6/esp_efuse_fields.c.obj
.PHONY : esp32c6/esp_efuse_fields.obj

# target to build an object file
esp32c6/esp_efuse_fields.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_fields.c.obj
.PHONY : esp32c6/esp_efuse_fields.c.obj

esp32c6/esp_efuse_fields.i: esp32c6/esp_efuse_fields.c.i
.PHONY : esp32c6/esp_efuse_fields.i

# target to preprocess a source file
esp32c6/esp_efuse_fields.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_fields.c.i
.PHONY : esp32c6/esp_efuse_fields.c.i

esp32c6/esp_efuse_fields.s: esp32c6/esp_efuse_fields.c.s
.PHONY : esp32c6/esp_efuse_fields.s

# target to generate assembly for a file
esp32c6/esp_efuse_fields.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_fields.c.s
.PHONY : esp32c6/esp_efuse_fields.c.s

esp32c6/esp_efuse_rtc_calib.obj: esp32c6/esp_efuse_rtc_calib.c.obj
.PHONY : esp32c6/esp_efuse_rtc_calib.obj

# target to build an object file
esp32c6/esp_efuse_rtc_calib.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_rtc_calib.c.obj
.PHONY : esp32c6/esp_efuse_rtc_calib.c.obj

esp32c6/esp_efuse_rtc_calib.i: esp32c6/esp_efuse_rtc_calib.c.i
.PHONY : esp32c6/esp_efuse_rtc_calib.i

# target to preprocess a source file
esp32c6/esp_efuse_rtc_calib.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_rtc_calib.c.i
.PHONY : esp32c6/esp_efuse_rtc_calib.c.i

esp32c6/esp_efuse_rtc_calib.s: esp32c6/esp_efuse_rtc_calib.c.s
.PHONY : esp32c6/esp_efuse_rtc_calib.s

# target to generate assembly for a file
esp32c6/esp_efuse_rtc_calib.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_rtc_calib.c.s
.PHONY : esp32c6/esp_efuse_rtc_calib.c.s

esp32c6/esp_efuse_table.obj: esp32c6/esp_efuse_table.c.obj
.PHONY : esp32c6/esp_efuse_table.obj

# target to build an object file
esp32c6/esp_efuse_table.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_table.c.obj
.PHONY : esp32c6/esp_efuse_table.c.obj

esp32c6/esp_efuse_table.i: esp32c6/esp_efuse_table.c.i
.PHONY : esp32c6/esp_efuse_table.i

# target to preprocess a source file
esp32c6/esp_efuse_table.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_table.c.i
.PHONY : esp32c6/esp_efuse_table.c.i

esp32c6/esp_efuse_table.s: esp32c6/esp_efuse_table.c.s
.PHONY : esp32c6/esp_efuse_table.s

# target to generate assembly for a file
esp32c6/esp_efuse_table.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_table.c.s
.PHONY : esp32c6/esp_efuse_table.c.s

esp32c6/esp_efuse_utility.obj: esp32c6/esp_efuse_utility.c.obj
.PHONY : esp32c6/esp_efuse_utility.obj

# target to build an object file
esp32c6/esp_efuse_utility.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_utility.c.obj
.PHONY : esp32c6/esp_efuse_utility.c.obj

esp32c6/esp_efuse_utility.i: esp32c6/esp_efuse_utility.c.i
.PHONY : esp32c6/esp_efuse_utility.i

# target to preprocess a source file
esp32c6/esp_efuse_utility.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_utility.c.i
.PHONY : esp32c6/esp_efuse_utility.c.i

esp32c6/esp_efuse_utility.s: esp32c6/esp_efuse_utility.c.s
.PHONY : esp32c6/esp_efuse_utility.s

# target to generate assembly for a file
esp32c6/esp_efuse_utility.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c6/esp_efuse_utility.c.s
.PHONY : esp32c6/esp_efuse_utility.c.s

src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.obj: src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj
.PHONY : src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.obj

# target to build an object file
src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj
.PHONY : src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj

src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.i: src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.i
.PHONY : src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.i

# target to preprocess a source file
src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.i
.PHONY : src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.i

src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.s: src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.s
.PHONY : src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.s

# target to generate assembly for a file
src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.s
.PHONY : src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.s

src/esp_efuse_api.obj: src/esp_efuse_api.c.obj
.PHONY : src/esp_efuse_api.obj

# target to build an object file
src/esp_efuse_api.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj
.PHONY : src/esp_efuse_api.c.obj

src/esp_efuse_api.i: src/esp_efuse_api.c.i
.PHONY : src/esp_efuse_api.i

# target to preprocess a source file
src/esp_efuse_api.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.i
.PHONY : src/esp_efuse_api.c.i

src/esp_efuse_api.s: src/esp_efuse_api.c.s
.PHONY : src/esp_efuse_api.s

# target to generate assembly for a file
src/esp_efuse_api.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.s
.PHONY : src/esp_efuse_api.c.s

src/esp_efuse_fields.obj: src/esp_efuse_fields.c.obj
.PHONY : src/esp_efuse_fields.obj

# target to build an object file
src/esp_efuse_fields.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj
.PHONY : src/esp_efuse_fields.c.obj

src/esp_efuse_fields.i: src/esp_efuse_fields.c.i
.PHONY : src/esp_efuse_fields.i

# target to preprocess a source file
src/esp_efuse_fields.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.i
.PHONY : src/esp_efuse_fields.c.i

src/esp_efuse_fields.s: src/esp_efuse_fields.c.s
.PHONY : src/esp_efuse_fields.s

# target to generate assembly for a file
src/esp_efuse_fields.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.s
.PHONY : src/esp_efuse_fields.c.s

src/esp_efuse_utility.obj: src/esp_efuse_utility.c.obj
.PHONY : src/esp_efuse_utility.obj

# target to build an object file
src/esp_efuse_utility.c.obj:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj
.PHONY : src/esp_efuse_utility.c.obj

src/esp_efuse_utility.i: src/esp_efuse_utility.c.i
.PHONY : src/esp_efuse_utility.i

# target to preprocess a source file
src/esp_efuse_utility.c.i:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.i
.PHONY : src/esp_efuse_utility.c.i

src/esp_efuse_utility.s: src/esp_efuse_utility.c.s
.PHONY : src/esp_efuse_utility.s

# target to generate assembly for a file
src/esp_efuse_utility.c.s:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(MAKE) $(MAKESILENT) -f esp-idf/efuse/CMakeFiles/__idf_efuse.dir/build.make esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.s
.PHONY : src/esp_efuse_utility.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... efuse-common-table"
	@echo "... efuse-custom-table"
	@echo "... efuse_common_table"
	@echo "... efuse_custom_table"
	@echo "... efuse_test_table"
	@echo "... show-efuse-table"
	@echo "... show_efuse_table"
	@echo "... __idf_efuse"
	@echo "... esp32c6/esp_efuse_fields.obj"
	@echo "... esp32c6/esp_efuse_fields.i"
	@echo "... esp32c6/esp_efuse_fields.s"
	@echo "... esp32c6/esp_efuse_rtc_calib.obj"
	@echo "... esp32c6/esp_efuse_rtc_calib.i"
	@echo "... esp32c6/esp_efuse_rtc_calib.s"
	@echo "... esp32c6/esp_efuse_table.obj"
	@echo "... esp32c6/esp_efuse_table.i"
	@echo "... esp32c6/esp_efuse_table.s"
	@echo "... esp32c6/esp_efuse_utility.obj"
	@echo "... esp32c6/esp_efuse_utility.i"
	@echo "... esp32c6/esp_efuse_utility.s"
	@echo "... src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.obj"
	@echo "... src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.i"
	@echo "... src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.s"
	@echo "... src/esp_efuse_api.obj"
	@echo "... src/esp_efuse_api.i"
	@echo "... src/esp_efuse_api.s"
	@echo "... src/esp_efuse_fields.obj"
	@echo "... src/esp_efuse_fields.i"
	@echo "... src/esp_efuse_fields.s"
	@echo "... src/esp_efuse_utility.obj"
	@echo "... src/esp_efuse_utility.i"
	@echo "... src/esp_efuse_utility.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/test/coap/examples/coap_client/build/bootloader && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

