# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj: /home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_crc.c \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_default_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/features.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_newlib_version.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_intsup.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_stdint.h \
  /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/esp_rom_caps.h

esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj: /home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_efuse.c \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_default_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/features.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_newlib_version.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_intsup.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_stdint.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_assert.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/assert.h \
  config/sdkconfig.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/ieeefp.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/newlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/config.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stddef.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_types.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/lock.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/lock.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/cdefs.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/alloca.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_locale.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/assert.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_bit_defs.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/reg_base.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/efuse_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/efuse_defs.h

esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_hp_regi2c_esp32c6.c.obj: /home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_hp_regi2c_esp32c6.c \
  /home/<USER>/esp-idf-anviz/components/esp_rom/include/esp_rom_sys.h \
  config/sdkconfig.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_default_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/features.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_newlib_version.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_intsup.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_stdint.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/reset_reasons.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_attr.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/i2c_ana_mst_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_assert.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/assert.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/ieeefp.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/newlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/config.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stddef.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_types.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/lock.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/lock.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/cdefs.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/alloca.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_locale.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/assert.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_bit_defs.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/reg_base.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/modem/modem_lpcon_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/modem/reg_base.h

esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj: /home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_spiflash.c \
  config/sdkconfig.h \
  /home/<USER>/esp-idf-anviz/components/soc/include/soc/spi_periph.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_default_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/features.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_newlib_version.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_intsup.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_stdint.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_assert.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/assert.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/ieeefp.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/newlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/config.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stddef.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_types.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/lock.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/lock.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/cdefs.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/alloca.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_locale.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/assert.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_bit_defs.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/reg_base.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/periph_defs.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc_caps.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc_pins.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/gpio_pins.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/spi_pins.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/spi_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/spi_struct.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/gpio_sig_map.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/spi_mem_struct.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/spi_mem_reg.h \
  /home/<USER>/esp-idf-anviz/components/esp_rom/include/esp_rom_spiflash.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdbool.h \
  /home/<USER>/esp-idf-anviz/components/esp_rom/include/esp_rom_spiflash_defs.h

esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj: /home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_sys.c \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_default_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/features.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_newlib_version.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_intsup.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdbool.h \
  config/sdkconfig.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_attr.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc_caps.h \
  /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/esp_rom_caps.h

esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj: /home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_systimer.c \
  config/sdkconfig.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stddef.h \
  /home/<USER>/esp-idf-anviz/components/hal/include/hal/systimer_hal.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_default_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/features.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_newlib_version.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_intsup.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdbool.h \
  /home/<USER>/esp-idf-anviz/components/hal/include/hal/systimer_types.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc_caps.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_assert.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/assert.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/ieeefp.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/newlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/config.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_types.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/lock.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/lock.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/cdefs.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/alloca.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_locale.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/assert.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/clk_tree_defs.h \
  /home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/systimer_ll.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/systimer_struct.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/pcr_struct.h \
  /home/<USER>/esp-idf-anviz/components/hal/platform_port/include/hal/assert.h

esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj: /home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_uart.c \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_default_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/features.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_newlib_version.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_intsup.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/ieeefp.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/newlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/config.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stddef.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_types.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/lock.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/lock.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/assert.h \
  config/sdkconfig.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/assert.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/cdefs.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/alloca.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_locale.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_attr.h \
  /home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/uart_ll.h \
  /home/<USER>/esp-idf-anviz/components/hal/platform_port/include/hal/misc.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/string.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/strings.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/string.h \
  /home/<USER>/esp-idf-anviz/components/hal/include/hal/uart_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdbool.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc_caps.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/clk_tree_defs.h \
  /home/<USER>/esp-idf-anviz/components/soc/include/soc/uart_periph.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/uart_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_assert.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_bit_defs.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/reg_base.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/uart_struct.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/periph_defs.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/gpio_sig_map.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/io_mux_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/uart_pins.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/pcr_struct.h \
  /home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/efuse_hal.h \
  /home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/efuse_ll.h \
  /home/<USER>/esp-idf-anviz/components/soc/include/soc/efuse_periph.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/efuse_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/efuse_defs.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/efuse_struct.h \
  /home/<USER>/esp-idf-anviz/components/hal/platform_port/include/hal/assert.h \
  /home/<USER>/esp-idf-anviz/components/esp_rom/include/esp32c6/rom/efuse.h \
  /home/<USER>/esp-idf-anviz/components/hal/include/hal/efuse_hal.h \
  /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/esp_rom_caps.h \
  /home/<USER>/esp-idf-anviz/components/esp_rom/include/esp32c6/rom/uart.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_types.h \
  /home/<USER>/esp-idf-anviz/components/esp_rom/include/esp32c6/rom/ets_sys.h

esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj: /home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_wdt.c \
  config/sdkconfig.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stddef.h \
  /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/esp_rom_caps.h \
  /home/<USER>/esp-idf-anviz/components/hal/include/hal/wdt_types.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/clk_tree_defs.h \
  /home/<USER>/esp-idf-anviz/components/hal/include/hal/wdt_hal.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdbool.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc_caps.h \
  /home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/mwdt_ll.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdint.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_default_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/features.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_newlib_version.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_intsup.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_stdint.h \
  /home/<USER>/esp-idf-anviz/components/soc/include/soc/timer_periph.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/timer_group_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_assert.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/assert.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/ieeefp.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/newlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/config.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/reent.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_types.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_types.h \
  /home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/lock.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/lock.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/cdefs.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/stdlib.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/alloca.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_locale.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/assert.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_bit_defs.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/reg_base.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/timer_group_struct.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/periph_defs.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/pcr_struct.h \
  /home/<USER>/esp-idf-anviz/components/hal/platform_port/include/hal/assert.h \
  /home/<USER>/esp-idf-anviz/components/esp_common/include/esp_attr.h \
  /home/<USER>/esp-idf-anviz/components/hal/platform_port/include/hal/misc.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/string.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/strings.h \
  /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/string.h \
  /home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/rwdt_ll.h \
  /home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/lpwdt_ll.h \
  /home/<USER>/esp-idf-anviz/components/soc/include/soc/rtc_cntl_periph.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_aon_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_analog_peri_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_clkrst_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_clkrst_struct.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_i2c_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_i2c_struct.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_io_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_io_struct.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_timer_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_timer_struct.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_uart_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_uart_struct.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_wdt_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_wdt_struct.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/efuse_reg.h \
  /home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/efuse_defs.h \
  /home/<USER>/esp-idf-anviz/components/esp_rom/include/esp32c6/rom/ets_sys.h


/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_uart_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_uart_struct.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_timer_struct.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_clkrst_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_analog_peri_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/include/soc/rtc_cntl_periph.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_clkrst_struct.h:

/home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/lpwdt_ll.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/timer_group_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/include/soc/timer_periph.h:

/home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/mwdt_ll.h:

/home/<USER>/esp-idf-anviz/components/hal/include/hal/wdt_hal.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_wdt.c:

/home/<USER>/esp-idf-anviz/components/esp_rom/include/esp32c6/rom/uart.h:

/home/<USER>/esp-idf-anviz/components/soc/include/soc/efuse_periph.h:

/home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/efuse_hal.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/uart_pins.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/io_mux_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/uart_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/include/soc/uart_periph.h:

/home/<USER>/esp-idf-anviz/components/esp_common/include/esp_types.h:

/home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/efuse_ll.h:

/home/<USER>/esp-idf-anviz/components/hal/include/hal/uart_types.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/string.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/strings.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/string.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/uart_struct.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_uart.c:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_io_struct.h:

/home/<USER>/esp-idf-anviz/components/hal/platform_port/include/hal/assert.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/pcr_struct.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/systimer_struct.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/timer_group_struct.h:

/home/<USER>/esp-idf-anviz/components/hal/include/hal/systimer_types.h:

/home/<USER>/esp-idf-anviz/components/hal/include/hal/systimer_hal.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/clk_tree_defs.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/cdefs.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/esp_rom_caps.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/efuse_struct.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/lock.h:

/home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/lock.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_wdt_reg.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_newlib_version.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/alloca.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdint.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/reent.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_locale.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_i2c_reg.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stddef.h:

/home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/rwdt_ll.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_crc.c:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/newlib.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_io_reg.h:

/home/<USER>/esp-idf-anviz/components/newlib/platform_include/sys/reent.h:

/home/<USER>/esp-idf-anviz/components/hal/include/hal/efuse_hal.h:

/home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/uart_ll.h:

config/sdkconfig.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_wdt_struct.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdlib.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/config.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_types.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/spi_struct.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/include/esp_rom_sys.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/stdint.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/features.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/include/esp32c6/rom/ets_sys.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/ieeefp.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/assert.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_stdint.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/_ansi.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_intsup.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/reg_base.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/stdlib.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_efuse.c:

/home/<USER>/esp-idf-anviz/components/newlib/platform_include/assert.h:

/home/<USER>/esp-idf-anviz/components/esp_common/include/esp_bit_defs.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_spiflash.c:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/reset_reasons.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/spi_mem_struct.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_timer_reg.h:

/home/<USER>/esp-idf-anviz/components/esp_common/include/esp_attr.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/include/esp32c6/rom/efuse.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/machine/_default_types.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/modem/modem_lpcon_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/i2c_ana_mst_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/spi_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_i2c_struct.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_sys.c:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/lp_aon_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/modem/reg_base.h:

/home/<USER>/esp-idf-anviz/components/soc/include/soc/spi_periph.h:

/home/<USER>/esp-idf-anviz/components/hal/platform_port/include/hal/misc.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/periph_defs.h:

/home/<USER>/esp-idf-anviz/components/hal/include/hal/wdt_types.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc_pins.h:

/home/<USER>/esp-idf-anviz/components/esp_common/include/esp_assert.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/efuse_defs.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_hp_regi2c_esp32c6.c:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/spi_pins.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/gpio_pins.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/efuse_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/gpio_sig_map.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/spi_mem_reg.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/include/esp_rom_spiflash.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/lib/gcc/riscv32-esp-elf/12.2.0/include/stdbool.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/include/esp_rom_spiflash_defs.h:

/home/<USER>/esp-idf-anviz/components/hal/esp32c6/include/hal/systimer_ll.h:

/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/riscv32-esp-elf/sys-include/sys/_types.h:

/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include/soc/soc_caps.h:

/home/<USER>/esp-idf-anviz/components/esp_rom/patches/esp_rom_systimer.c:
