# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test/coap/examples/coap_client

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test/coap/examples/coap_client/build

# Include any dependencies generated for this target.
include CMakeFiles/coap_client.elf.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/coap_client.elf.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/coap_client.elf.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/coap_client.elf.dir/flags.make

project_elf_src_esp32c6.c:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating project_elf_src_esp32c6.c"
	/usr/bin/cmake -E touch /home/<USER>/test/coap/examples/coap_client/build/project_elf_src_esp32c6.c

CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.obj: CMakeFiles/coap_client.elf.dir/flags.make
CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.obj: project_elf_src_esp32c6.c
CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.obj: CMakeFiles/coap_client.elf.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.obj"
	/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.obj -MF CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.obj.d -o CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.obj -c /home/<USER>/test/coap/examples/coap_client/build/project_elf_src_esp32c6.c

CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.i"
	/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/test/coap/examples/coap_client/build/project_elf_src_esp32c6.c > CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.i

CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.s"
	/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/test/coap/examples/coap_client/build/project_elf_src_esp32c6.c -o CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.s

# Object files for target coap_client.elf
coap_client_elf_OBJECTS = \
"CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.obj"

# External object files for target coap_client.elf
coap_client_elf_EXTERNAL_OBJECTS =

coap_client.elf: CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.obj
coap_client.elf: CMakeFiles/coap_client.elf.dir/build.make
coap_client.elf: esp-idf/riscv/libriscv.a
coap_client.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
coap_client.elf: esp-idf/efuse/libefuse.a
coap_client.elf: esp-idf/driver/libdriver.a
coap_client.elf: esp-idf/esp_pm/libesp_pm.a
coap_client.elf: esp-idf/mbedtls/libmbedtls.a
coap_client.elf: esp-idf/esp_app_format/libesp_app_format.a
coap_client.elf: esp-idf/bootloader_support/libbootloader_support.a
coap_client.elf: esp-idf/esp_partition/libesp_partition.a
coap_client.elf: esp-idf/app_update/libapp_update.a
coap_client.elf: esp-idf/esp_mm/libesp_mm.a
coap_client.elf: esp-idf/spi_flash/libspi_flash.a
coap_client.elf: esp-idf/pthread/libpthread.a
coap_client.elf: esp-idf/esp_system/libesp_system.a
coap_client.elf: esp-idf/esp_rom/libesp_rom.a
coap_client.elf: esp-idf/hal/libhal.a
coap_client.elf: esp-idf/log/liblog.a
coap_client.elf: esp-idf/heap/libheap.a
coap_client.elf: esp-idf/soc/libsoc.a
coap_client.elf: esp-idf/esp_hw_support/libesp_hw_support.a
coap_client.elf: esp-idf/freertos/libfreertos.a
coap_client.elf: esp-idf/newlib/libnewlib.a
coap_client.elf: esp-idf/cxx/libcxx.a
coap_client.elf: esp-idf/esp_common/libesp_common.a
coap_client.elf: esp-idf/esp_timer/libesp_timer.a
coap_client.elf: esp-idf/app_trace/libapp_trace.a
coap_client.elf: esp-idf/esp_event/libesp_event.a
coap_client.elf: esp-idf/nvs_flash/libnvs_flash.a
coap_client.elf: esp-idf/esp_phy/libesp_phy.a
coap_client.elf: esp-idf/vfs/libvfs.a
coap_client.elf: esp-idf/lwip/liblwip.a
coap_client.elf: esp-idf/esp_netif/libesp_netif.a
coap_client.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
coap_client.elf: esp-idf/esp_coex/libesp_coex.a
coap_client.elf: esp-idf/esp_wifi/libesp_wifi.a
coap_client.elf: esp-idf/unity/libunity.a
coap_client.elf: esp-idf/cmock/libcmock.a
coap_client.elf: esp-idf/console/libconsole.a
coap_client.elf: esp-idf/http_parser/libhttp_parser.a
coap_client.elf: esp-idf/esp-tls/libesp-tls.a
coap_client.elf: esp-idf/esp_adc/libesp_adc.a
coap_client.elf: esp-idf/esp_eth/libesp_eth.a
coap_client.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
coap_client.elf: esp-idf/esp_hid/libesp_hid.a
coap_client.elf: esp-idf/tcp_transport/libtcp_transport.a
coap_client.elf: esp-idf/esp_http_client/libesp_http_client.a
coap_client.elf: esp-idf/esp_http_server/libesp_http_server.a
coap_client.elf: esp-idf/esp_https_ota/libesp_https_ota.a
coap_client.elf: esp-idf/esp_lcd/libesp_lcd.a
coap_client.elf: esp-idf/protobuf-c/libprotobuf-c.a
coap_client.elf: esp-idf/protocomm/libprotocomm.a
coap_client.elf: esp-idf/esp_local_ctrl/libesp_local_ctrl.a
coap_client.elf: esp-idf/espcoredump/libespcoredump.a
coap_client.elf: esp-idf/wear_levelling/libwear_levelling.a
coap_client.elf: esp-idf/sdmmc/libsdmmc.a
coap_client.elf: esp-idf/fatfs/libfatfs.a
coap_client.elf: esp-idf/ieee802154/libieee802154.a
coap_client.elf: esp-idf/json/libjson.a
coap_client.elf: esp-idf/mqtt/libmqtt.a
coap_client.elf: esp-idf/spiffs/libspiffs.a
coap_client.elf: esp-idf/wifi_provisioning/libwifi_provisioning.a
coap_client.elf: esp-idf/coap/libcoap.a
coap_client.elf: esp-idf/main/libmain.a
coap_client.elf: esp-idf/protocol_examples_common/libprotocol_examples_common.a
coap_client.elf: esp-idf/app_trace/libapp_trace.a
coap_client.elf: esp-idf/app_trace/libapp_trace.a
coap_client.elf: esp-idf/cmock/libcmock.a
coap_client.elf: esp-idf/unity/libunity.a
coap_client.elf: esp-idf/esp_hid/libesp_hid.a
coap_client.elf: esp-idf/esp_lcd/libesp_lcd.a
coap_client.elf: esp-idf/esp_local_ctrl/libesp_local_ctrl.a
coap_client.elf: esp-idf/espcoredump/libespcoredump.a
coap_client.elf: esp-idf/fatfs/libfatfs.a
coap_client.elf: esp-idf/wear_levelling/libwear_levelling.a
coap_client.elf: esp-idf/sdmmc/libsdmmc.a
coap_client.elf: esp-idf/ieee802154/libieee802154.a
coap_client.elf: esp-idf/mqtt/libmqtt.a
coap_client.elf: esp-idf/spiffs/libspiffs.a
coap_client.elf: esp-idf/wifi_provisioning/libwifi_provisioning.a
coap_client.elf: esp-idf/protocomm/libprotocomm.a
coap_client.elf: esp-idf/console/libconsole.a
coap_client.elf: esp-idf/protobuf-c/libprotobuf-c.a
coap_client.elf: esp-idf/json/libjson.a
coap_client.elf: esp-idf/coap/libcoap.a
coap_client.elf: esp-idf/riscv/libriscv.a
coap_client.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
coap_client.elf: esp-idf/efuse/libefuse.a
coap_client.elf: esp-idf/driver/libdriver.a
coap_client.elf: esp-idf/esp_pm/libesp_pm.a
coap_client.elf: esp-idf/mbedtls/libmbedtls.a
coap_client.elf: esp-idf/esp_app_format/libesp_app_format.a
coap_client.elf: esp-idf/bootloader_support/libbootloader_support.a
coap_client.elf: esp-idf/esp_partition/libesp_partition.a
coap_client.elf: esp-idf/app_update/libapp_update.a
coap_client.elf: esp-idf/esp_mm/libesp_mm.a
coap_client.elf: esp-idf/spi_flash/libspi_flash.a
coap_client.elf: esp-idf/pthread/libpthread.a
coap_client.elf: esp-idf/esp_system/libesp_system.a
coap_client.elf: esp-idf/esp_rom/libesp_rom.a
coap_client.elf: esp-idf/hal/libhal.a
coap_client.elf: esp-idf/log/liblog.a
coap_client.elf: esp-idf/heap/libheap.a
coap_client.elf: esp-idf/soc/libsoc.a
coap_client.elf: esp-idf/esp_hw_support/libesp_hw_support.a
coap_client.elf: esp-idf/freertos/libfreertos.a
coap_client.elf: esp-idf/newlib/libnewlib.a
coap_client.elf: esp-idf/cxx/libcxx.a
coap_client.elf: esp-idf/esp_common/libesp_common.a
coap_client.elf: esp-idf/esp_timer/libesp_timer.a
coap_client.elf: esp-idf/esp_event/libesp_event.a
coap_client.elf: esp-idf/nvs_flash/libnvs_flash.a
coap_client.elf: esp-idf/esp_phy/libesp_phy.a
coap_client.elf: esp-idf/vfs/libvfs.a
coap_client.elf: esp-idf/lwip/liblwip.a
coap_client.elf: esp-idf/esp_netif/libesp_netif.a
coap_client.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
coap_client.elf: esp-idf/esp_coex/libesp_coex.a
coap_client.elf: esp-idf/esp_wifi/libesp_wifi.a
coap_client.elf: esp-idf/http_parser/libhttp_parser.a
coap_client.elf: esp-idf/esp-tls/libesp-tls.a
coap_client.elf: esp-idf/esp_adc/libesp_adc.a
coap_client.elf: esp-idf/esp_eth/libesp_eth.a
coap_client.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
coap_client.elf: esp-idf/tcp_transport/libtcp_transport.a
coap_client.elf: esp-idf/esp_http_client/libesp_http_client.a
coap_client.elf: esp-idf/esp_http_server/libesp_http_server.a
coap_client.elf: esp-idf/esp_https_ota/libesp_https_ota.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a
coap_client.elf: esp-idf/riscv/libriscv.a
coap_client.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
coap_client.elf: esp-idf/efuse/libefuse.a
coap_client.elf: esp-idf/driver/libdriver.a
coap_client.elf: esp-idf/esp_pm/libesp_pm.a
coap_client.elf: esp-idf/mbedtls/libmbedtls.a
coap_client.elf: esp-idf/esp_app_format/libesp_app_format.a
coap_client.elf: esp-idf/bootloader_support/libbootloader_support.a
coap_client.elf: esp-idf/esp_partition/libesp_partition.a
coap_client.elf: esp-idf/app_update/libapp_update.a
coap_client.elf: esp-idf/esp_mm/libesp_mm.a
coap_client.elf: esp-idf/spi_flash/libspi_flash.a
coap_client.elf: esp-idf/pthread/libpthread.a
coap_client.elf: esp-idf/esp_system/libesp_system.a
coap_client.elf: esp-idf/esp_rom/libesp_rom.a
coap_client.elf: esp-idf/hal/libhal.a
coap_client.elf: esp-idf/log/liblog.a
coap_client.elf: esp-idf/heap/libheap.a
coap_client.elf: esp-idf/soc/libsoc.a
coap_client.elf: esp-idf/esp_hw_support/libesp_hw_support.a
coap_client.elf: esp-idf/freertos/libfreertos.a
coap_client.elf: esp-idf/newlib/libnewlib.a
coap_client.elf: esp-idf/cxx/libcxx.a
coap_client.elf: esp-idf/esp_common/libesp_common.a
coap_client.elf: esp-idf/esp_timer/libesp_timer.a
coap_client.elf: esp-idf/esp_event/libesp_event.a
coap_client.elf: esp-idf/nvs_flash/libnvs_flash.a
coap_client.elf: esp-idf/esp_phy/libesp_phy.a
coap_client.elf: esp-idf/vfs/libvfs.a
coap_client.elf: esp-idf/lwip/liblwip.a
coap_client.elf: esp-idf/esp_netif/libesp_netif.a
coap_client.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
coap_client.elf: esp-idf/esp_coex/libesp_coex.a
coap_client.elf: esp-idf/esp_wifi/libesp_wifi.a
coap_client.elf: esp-idf/http_parser/libhttp_parser.a
coap_client.elf: esp-idf/esp-tls/libesp-tls.a
coap_client.elf: esp-idf/esp_adc/libesp_adc.a
coap_client.elf: esp-idf/esp_eth/libesp_eth.a
coap_client.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
coap_client.elf: esp-idf/tcp_transport/libtcp_transport.a
coap_client.elf: esp-idf/esp_http_client/libesp_http_client.a
coap_client.elf: esp-idf/esp_http_server/libesp_http_server.a
coap_client.elf: esp-idf/esp_https_ota/libesp_https_ota.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a
coap_client.elf: esp-idf/riscv/libriscv.a
coap_client.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
coap_client.elf: esp-idf/efuse/libefuse.a
coap_client.elf: esp-idf/driver/libdriver.a
coap_client.elf: esp-idf/esp_pm/libesp_pm.a
coap_client.elf: esp-idf/mbedtls/libmbedtls.a
coap_client.elf: esp-idf/esp_app_format/libesp_app_format.a
coap_client.elf: esp-idf/bootloader_support/libbootloader_support.a
coap_client.elf: esp-idf/esp_partition/libesp_partition.a
coap_client.elf: esp-idf/app_update/libapp_update.a
coap_client.elf: esp-idf/esp_mm/libesp_mm.a
coap_client.elf: esp-idf/spi_flash/libspi_flash.a
coap_client.elf: esp-idf/pthread/libpthread.a
coap_client.elf: esp-idf/esp_system/libesp_system.a
coap_client.elf: esp-idf/esp_rom/libesp_rom.a
coap_client.elf: esp-idf/hal/libhal.a
coap_client.elf: esp-idf/log/liblog.a
coap_client.elf: esp-idf/heap/libheap.a
coap_client.elf: esp-idf/soc/libsoc.a
coap_client.elf: esp-idf/esp_hw_support/libesp_hw_support.a
coap_client.elf: esp-idf/freertos/libfreertos.a
coap_client.elf: esp-idf/newlib/libnewlib.a
coap_client.elf: esp-idf/cxx/libcxx.a
coap_client.elf: esp-idf/esp_common/libesp_common.a
coap_client.elf: esp-idf/esp_timer/libesp_timer.a
coap_client.elf: esp-idf/esp_event/libesp_event.a
coap_client.elf: esp-idf/nvs_flash/libnvs_flash.a
coap_client.elf: esp-idf/esp_phy/libesp_phy.a
coap_client.elf: esp-idf/vfs/libvfs.a
coap_client.elf: esp-idf/lwip/liblwip.a
coap_client.elf: esp-idf/esp_netif/libesp_netif.a
coap_client.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
coap_client.elf: esp-idf/esp_coex/libesp_coex.a
coap_client.elf: esp-idf/esp_wifi/libesp_wifi.a
coap_client.elf: esp-idf/http_parser/libhttp_parser.a
coap_client.elf: esp-idf/esp-tls/libesp-tls.a
coap_client.elf: esp-idf/esp_adc/libesp_adc.a
coap_client.elf: esp-idf/esp_eth/libesp_eth.a
coap_client.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
coap_client.elf: esp-idf/tcp_transport/libtcp_transport.a
coap_client.elf: esp-idf/esp_http_client/libesp_http_client.a
coap_client.elf: esp-idf/esp_http_server/libesp_http_server.a
coap_client.elf: esp-idf/esp_https_ota/libesp_https_ota.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a
coap_client.elf: esp-idf/riscv/libriscv.a
coap_client.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
coap_client.elf: esp-idf/efuse/libefuse.a
coap_client.elf: esp-idf/driver/libdriver.a
coap_client.elf: esp-idf/esp_pm/libesp_pm.a
coap_client.elf: esp-idf/mbedtls/libmbedtls.a
coap_client.elf: esp-idf/esp_app_format/libesp_app_format.a
coap_client.elf: esp-idf/bootloader_support/libbootloader_support.a
coap_client.elf: esp-idf/esp_partition/libesp_partition.a
coap_client.elf: esp-idf/app_update/libapp_update.a
coap_client.elf: esp-idf/esp_mm/libesp_mm.a
coap_client.elf: esp-idf/spi_flash/libspi_flash.a
coap_client.elf: esp-idf/pthread/libpthread.a
coap_client.elf: esp-idf/esp_system/libesp_system.a
coap_client.elf: esp-idf/esp_rom/libesp_rom.a
coap_client.elf: esp-idf/hal/libhal.a
coap_client.elf: esp-idf/log/liblog.a
coap_client.elf: esp-idf/heap/libheap.a
coap_client.elf: esp-idf/soc/libsoc.a
coap_client.elf: esp-idf/esp_hw_support/libesp_hw_support.a
coap_client.elf: esp-idf/freertos/libfreertos.a
coap_client.elf: esp-idf/newlib/libnewlib.a
coap_client.elf: esp-idf/cxx/libcxx.a
coap_client.elf: esp-idf/esp_common/libesp_common.a
coap_client.elf: esp-idf/esp_timer/libesp_timer.a
coap_client.elf: esp-idf/esp_event/libesp_event.a
coap_client.elf: esp-idf/nvs_flash/libnvs_flash.a
coap_client.elf: esp-idf/esp_phy/libesp_phy.a
coap_client.elf: esp-idf/vfs/libvfs.a
coap_client.elf: esp-idf/lwip/liblwip.a
coap_client.elf: esp-idf/esp_netif/libesp_netif.a
coap_client.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
coap_client.elf: esp-idf/esp_coex/libesp_coex.a
coap_client.elf: esp-idf/esp_wifi/libesp_wifi.a
coap_client.elf: esp-idf/http_parser/libhttp_parser.a
coap_client.elf: esp-idf/esp-tls/libesp-tls.a
coap_client.elf: esp-idf/esp_adc/libesp_adc.a
coap_client.elf: esp-idf/esp_eth/libesp_eth.a
coap_client.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
coap_client.elf: esp-idf/tcp_transport/libtcp_transport.a
coap_client.elf: esp-idf/esp_http_client/libesp_http_client.a
coap_client.elf: esp-idf/esp_http_server/libesp_http_server.a
coap_client.elf: esp-idf/esp_https_ota/libesp_https_ota.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a
coap_client.elf: esp-idf/riscv/libriscv.a
coap_client.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
coap_client.elf: esp-idf/efuse/libefuse.a
coap_client.elf: esp-idf/driver/libdriver.a
coap_client.elf: esp-idf/esp_pm/libesp_pm.a
coap_client.elf: esp-idf/mbedtls/libmbedtls.a
coap_client.elf: esp-idf/esp_app_format/libesp_app_format.a
coap_client.elf: esp-idf/bootloader_support/libbootloader_support.a
coap_client.elf: esp-idf/esp_partition/libesp_partition.a
coap_client.elf: esp-idf/app_update/libapp_update.a
coap_client.elf: esp-idf/esp_mm/libesp_mm.a
coap_client.elf: esp-idf/spi_flash/libspi_flash.a
coap_client.elf: esp-idf/pthread/libpthread.a
coap_client.elf: esp-idf/esp_system/libesp_system.a
coap_client.elf: esp-idf/esp_rom/libesp_rom.a
coap_client.elf: esp-idf/hal/libhal.a
coap_client.elf: esp-idf/log/liblog.a
coap_client.elf: esp-idf/heap/libheap.a
coap_client.elf: esp-idf/soc/libsoc.a
coap_client.elf: esp-idf/esp_hw_support/libesp_hw_support.a
coap_client.elf: esp-idf/freertos/libfreertos.a
coap_client.elf: esp-idf/newlib/libnewlib.a
coap_client.elf: esp-idf/cxx/libcxx.a
coap_client.elf: esp-idf/esp_common/libesp_common.a
coap_client.elf: esp-idf/esp_timer/libesp_timer.a
coap_client.elf: esp-idf/esp_event/libesp_event.a
coap_client.elf: esp-idf/nvs_flash/libnvs_flash.a
coap_client.elf: esp-idf/esp_phy/libesp_phy.a
coap_client.elf: esp-idf/vfs/libvfs.a
coap_client.elf: esp-idf/lwip/liblwip.a
coap_client.elf: esp-idf/esp_netif/libesp_netif.a
coap_client.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
coap_client.elf: esp-idf/esp_coex/libesp_coex.a
coap_client.elf: esp-idf/esp_wifi/libesp_wifi.a
coap_client.elf: esp-idf/http_parser/libhttp_parser.a
coap_client.elf: esp-idf/esp-tls/libesp-tls.a
coap_client.elf: esp-idf/esp_adc/libesp_adc.a
coap_client.elf: esp-idf/esp_eth/libesp_eth.a
coap_client.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
coap_client.elf: esp-idf/tcp_transport/libtcp_transport.a
coap_client.elf: esp-idf/esp_http_client/libesp_http_client.a
coap_client.elf: esp-idf/esp_http_server/libesp_http_server.a
coap_client.elf: esp-idf/esp_https_ota/libesp_https_ota.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a
coap_client.elf: esp-idf/riscv/libriscv.a
coap_client.elf: esp-idf/esp_ringbuf/libesp_ringbuf.a
coap_client.elf: esp-idf/efuse/libefuse.a
coap_client.elf: esp-idf/driver/libdriver.a
coap_client.elf: esp-idf/esp_pm/libesp_pm.a
coap_client.elf: esp-idf/mbedtls/libmbedtls.a
coap_client.elf: esp-idf/esp_app_format/libesp_app_format.a
coap_client.elf: esp-idf/bootloader_support/libbootloader_support.a
coap_client.elf: esp-idf/esp_partition/libesp_partition.a
coap_client.elf: esp-idf/app_update/libapp_update.a
coap_client.elf: esp-idf/esp_mm/libesp_mm.a
coap_client.elf: esp-idf/spi_flash/libspi_flash.a
coap_client.elf: esp-idf/pthread/libpthread.a
coap_client.elf: esp-idf/esp_system/libesp_system.a
coap_client.elf: esp-idf/esp_rom/libesp_rom.a
coap_client.elf: esp-idf/hal/libhal.a
coap_client.elf: esp-idf/log/liblog.a
coap_client.elf: esp-idf/heap/libheap.a
coap_client.elf: esp-idf/soc/libsoc.a
coap_client.elf: esp-idf/esp_hw_support/libesp_hw_support.a
coap_client.elf: esp-idf/freertos/libfreertos.a
coap_client.elf: esp-idf/newlib/libnewlib.a
coap_client.elf: esp-idf/cxx/libcxx.a
coap_client.elf: esp-idf/esp_common/libesp_common.a
coap_client.elf: esp-idf/esp_timer/libesp_timer.a
coap_client.elf: esp-idf/esp_event/libesp_event.a
coap_client.elf: esp-idf/nvs_flash/libnvs_flash.a
coap_client.elf: esp-idf/esp_phy/libesp_phy.a
coap_client.elf: esp-idf/vfs/libvfs.a
coap_client.elf: esp-idf/lwip/liblwip.a
coap_client.elf: esp-idf/esp_netif/libesp_netif.a
coap_client.elf: esp-idf/wpa_supplicant/libwpa_supplicant.a
coap_client.elf: esp-idf/esp_coex/libesp_coex.a
coap_client.elf: esp-idf/esp_wifi/libesp_wifi.a
coap_client.elf: esp-idf/http_parser/libhttp_parser.a
coap_client.elf: esp-idf/esp-tls/libesp-tls.a
coap_client.elf: esp-idf/esp_adc/libesp_adc.a
coap_client.elf: esp-idf/esp_eth/libesp_eth.a
coap_client.elf: esp-idf/esp_gdbstub/libesp_gdbstub.a
coap_client.elf: esp-idf/tcp_transport/libtcp_transport.a
coap_client.elf: esp-idf/esp_http_client/libesp_http_client.a
coap_client.elf: esp-idf/esp_http_server/libesp_http_server.a
coap_client.elf: esp-idf/esp_https_ota/libesp_https_ota.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedtls.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a
coap_client.elf: esp-idf/mbedtls/mbedtls/library/libmbedx509.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a
coap_client.elf: esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a
coap_client.elf: esp-idf/newlib/libnewlib.a
coap_client.elf: esp-idf/pthread/libpthread.a
coap_client.elf: esp-idf/cxx/libcxx.a
coap_client.elf: esp-idf/esp_phy/libesp_phy.a
coap_client.elf: esp-idf/esp_phy/libesp_phy.a
coap_client.elf: esp-idf/esp_system/ld/memory.ld
coap_client.elf: esp-idf/esp_system/ld/sections.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.api.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.rvfp.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.newlib.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.version.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.phy.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.coexist.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.net80211.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.pp.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.wdt.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.newlib-normal.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld/esp32c6.rom.heap.ld
coap_client.elf: /home/<USER>/esp-idf-anviz/components/soc/esp32c6/ld/esp32c6.peripherals.ld
coap_client.elf: CMakeFiles/coap_client.elf.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/test/coap/examples/coap_client/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable coap_client.elf"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/coap_client.elf.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/coap_client.elf.dir/build: coap_client.elf
.PHONY : CMakeFiles/coap_client.elf.dir/build

CMakeFiles/coap_client.elf.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/coap_client.elf.dir/cmake_clean.cmake
.PHONY : CMakeFiles/coap_client.elf.dir/clean

CMakeFiles/coap_client.elf.dir/depend: project_elf_src_esp32c6.c
	cd /home/<USER>/test/coap/examples/coap_client/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/test/coap/examples/coap_client /home/<USER>/test/coap/examples/coap_client /home/<USER>/test/coap/examples/coap_client/build /home/<USER>/test/coap/examples/coap_client/build /home/<USER>/test/coap/examples/coap_client/build/CMakeFiles/coap_client.elf.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/coap_client.elf.dir/depend

