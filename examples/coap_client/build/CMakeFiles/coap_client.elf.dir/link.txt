/home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-g++ -march=rv32imac_zicsr_zifencei  -nostartfiles -march=rv32imac_zicsr_zifencei --specs=nosys.specs  -Wl,--cref -Wl,--defsym=IDF_TARGET_ESP32C6=0 -Wl,--Map=/home/<USER>/test/coap/examples/coap_client/build/coap_client.map -Wl,--no-warn-rwx-segments -fno-rtti -fno-lto -Wl,--gc-sections -Wl,--warn-common -T esp32c6.peripherals.ld -T esp32c6.rom.ld -T esp32c6.rom.api.ld -T esp32c6.rom.rvfp.ld -T esp32c6.rom.newlib.ld -T esp32c6.rom.version.ld -T esp32c6.rom.phy.ld -T esp32c6.rom.coexist.ld -T esp32c6.rom.net80211.ld -T esp32c6.rom.pp.ld -T esp32c6.rom.wdt.ld -T esp32c6.rom.newlib-normal.ld -T esp32c6.rom.heap.ld -T memory.ld -T sections.ld CMakeFiles/coap_client.elf.dir/project_elf_src_esp32c6.c.obj -o coap_client.elf   -L/home/<USER>/esp-idf-anviz/components/soc/esp32c6/ld  -L/home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6/ld  -L/home/<USER>/test/coap/examples/coap_client/build/esp-idf/esp_system/ld  -L/home/<USER>/esp-idf-anviz/components/esp_phy/lib/esp32c6  -L/home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6  -L/home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6  esp-idf/riscv/libriscv.a esp-idf/esp_ringbuf/libesp_ringbuf.a esp-idf/efuse/libefuse.a esp-idf/driver/libdriver.a esp-idf/esp_pm/libesp_pm.a esp-idf/mbedtls/libmbedtls.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/esp_partition/libesp_partition.a esp-idf/app_update/libapp_update.a esp-idf/esp_mm/libesp_mm.a esp-idf/spi_flash/libspi_flash.a esp-idf/pthread/libpthread.a esp-idf/esp_system/libesp_system.a esp-idf/esp_rom/libesp_rom.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/heap/libheap.a esp-idf/soc/libsoc.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/freertos/libfreertos.a esp-idf/newlib/libnewlib.a esp-idf/cxx/libcxx.a esp-idf/esp_common/libesp_common.a esp-idf/esp_timer/libesp_timer.a esp-idf/app_trace/libapp_trace.a esp-idf/esp_event/libesp_event.a esp-idf/nvs_flash/libnvs_flash.a esp-idf/esp_phy/libesp_phy.a esp-idf/vfs/libvfs.a esp-idf/lwip/liblwip.a esp-idf/esp_netif/libesp_netif.a esp-idf/wpa_supplicant/libwpa_supplicant.a esp-idf/esp_coex/libesp_coex.a esp-idf/esp_wifi/libesp_wifi.a esp-idf/unity/libunity.a esp-idf/cmock/libcmock.a esp-idf/console/libconsole.a esp-idf/http_parser/libhttp_parser.a esp-idf/esp-tls/libesp-tls.a esp-idf/esp_adc/libesp_adc.a esp-idf/esp_eth/libesp_eth.a esp-idf/esp_gdbstub/libesp_gdbstub.a esp-idf/esp_hid/libesp_hid.a esp-idf/tcp_transport/libtcp_transport.a esp-idf/esp_http_client/libesp_http_client.a esp-idf/esp_http_server/libesp_http_server.a esp-idf/esp_https_ota/libesp_https_ota.a esp-idf/esp_lcd/libesp_lcd.a esp-idf/protobuf-c/libprotobuf-c.a esp-idf/protocomm/libprotocomm.a esp-idf/esp_local_ctrl/libesp_local_ctrl.a esp-idf/espcoredump/libespcoredump.a esp-idf/wear_levelling/libwear_levelling.a esp-idf/sdmmc/libsdmmc.a esp-idf/fatfs/libfatfs.a esp-idf/ieee802154/libieee802154.a esp-idf/json/libjson.a esp-idf/mqtt/libmqtt.a esp-idf/spiffs/libspiffs.a esp-idf/wifi_provisioning/libwifi_provisioning.a esp-idf/coap/libcoap.a esp-idf/main/libmain.a esp-idf/protocol_examples_common/libprotocol_examples_common.a esp-idf/app_trace/libapp_trace.a esp-idf/app_trace/libapp_trace.a esp-idf/cmock/libcmock.a esp-idf/unity/libunity.a esp-idf/esp_hid/libesp_hid.a esp-idf/esp_lcd/libesp_lcd.a esp-idf/esp_local_ctrl/libesp_local_ctrl.a esp-idf/espcoredump/libespcoredump.a esp-idf/fatfs/libfatfs.a esp-idf/wear_levelling/libwear_levelling.a esp-idf/sdmmc/libsdmmc.a esp-idf/ieee802154/libieee802154.a esp-idf/mqtt/libmqtt.a esp-idf/spiffs/libspiffs.a esp-idf/wifi_provisioning/libwifi_provisioning.a esp-idf/protocomm/libprotocomm.a esp-idf/console/libconsole.a esp-idf/protobuf-c/libprotobuf-c.a esp-idf/json/libjson.a esp-idf/coap/libcoap.a esp-idf/riscv/libriscv.a esp-idf/esp_ringbuf/libesp_ringbuf.a esp-idf/efuse/libefuse.a esp-idf/driver/libdriver.a esp-idf/esp_pm/libesp_pm.a esp-idf/mbedtls/libmbedtls.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/esp_partition/libesp_partition.a esp-idf/app_update/libapp_update.a esp-idf/esp_mm/libesp_mm.a esp-idf/spi_flash/libspi_flash.a esp-idf/pthread/libpthread.a esp-idf/esp_system/libesp_system.a esp-idf/esp_rom/libesp_rom.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/heap/libheap.a esp-idf/soc/libsoc.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/freertos/libfreertos.a esp-idf/newlib/libnewlib.a esp-idf/cxx/libcxx.a esp-idf/esp_common/libesp_common.a esp-idf/esp_timer/libesp_timer.a esp-idf/esp_event/libesp_event.a esp-idf/nvs_flash/libnvs_flash.a esp-idf/esp_phy/libesp_phy.a esp-idf/vfs/libvfs.a esp-idf/lwip/liblwip.a esp-idf/esp_netif/libesp_netif.a esp-idf/wpa_supplicant/libwpa_supplicant.a esp-idf/esp_coex/libesp_coex.a esp-idf/esp_wifi/libesp_wifi.a esp-idf/http_parser/libhttp_parser.a esp-idf/esp-tls/libesp-tls.a esp-idf/esp_adc/libesp_adc.a esp-idf/esp_eth/libesp_eth.a esp-idf/esp_gdbstub/libesp_gdbstub.a esp-idf/tcp_transport/libtcp_transport.a esp-idf/esp_http_client/libesp_http_client.a esp-idf/esp_http_server/libesp_http_server.a esp-idf/esp_https_ota/libesp_https_ota.a esp-idf/mbedtls/mbedtls/library/libmbedtls.a esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a esp-idf/mbedtls/mbedtls/library/libmbedx509.a esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a esp-idf/riscv/libriscv.a esp-idf/esp_ringbuf/libesp_ringbuf.a esp-idf/efuse/libefuse.a esp-idf/driver/libdriver.a esp-idf/esp_pm/libesp_pm.a esp-idf/mbedtls/libmbedtls.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/esp_partition/libesp_partition.a esp-idf/app_update/libapp_update.a esp-idf/esp_mm/libesp_mm.a esp-idf/spi_flash/libspi_flash.a esp-idf/pthread/libpthread.a esp-idf/esp_system/libesp_system.a esp-idf/esp_rom/libesp_rom.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/heap/libheap.a esp-idf/soc/libsoc.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/freertos/libfreertos.a esp-idf/newlib/libnewlib.a esp-idf/cxx/libcxx.a esp-idf/esp_common/libesp_common.a esp-idf/esp_timer/libesp_timer.a esp-idf/esp_event/libesp_event.a esp-idf/nvs_flash/libnvs_flash.a esp-idf/esp_phy/libesp_phy.a esp-idf/vfs/libvfs.a esp-idf/lwip/liblwip.a esp-idf/esp_netif/libesp_netif.a esp-idf/wpa_supplicant/libwpa_supplicant.a esp-idf/esp_coex/libesp_coex.a esp-idf/esp_wifi/libesp_wifi.a esp-idf/http_parser/libhttp_parser.a esp-idf/esp-tls/libesp-tls.a esp-idf/esp_adc/libesp_adc.a esp-idf/esp_eth/libesp_eth.a esp-idf/esp_gdbstub/libesp_gdbstub.a esp-idf/tcp_transport/libtcp_transport.a esp-idf/esp_http_client/libesp_http_client.a esp-idf/esp_http_server/libesp_http_server.a esp-idf/esp_https_ota/libesp_https_ota.a esp-idf/mbedtls/mbedtls/library/libmbedtls.a esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a esp-idf/mbedtls/mbedtls/library/libmbedx509.a esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a esp-idf/riscv/libriscv.a esp-idf/esp_ringbuf/libesp_ringbuf.a esp-idf/efuse/libefuse.a esp-idf/driver/libdriver.a esp-idf/esp_pm/libesp_pm.a esp-idf/mbedtls/libmbedtls.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/esp_partition/libesp_partition.a esp-idf/app_update/libapp_update.a esp-idf/esp_mm/libesp_mm.a esp-idf/spi_flash/libspi_flash.a esp-idf/pthread/libpthread.a esp-idf/esp_system/libesp_system.a esp-idf/esp_rom/libesp_rom.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/heap/libheap.a esp-idf/soc/libsoc.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/freertos/libfreertos.a esp-idf/newlib/libnewlib.a esp-idf/cxx/libcxx.a esp-idf/esp_common/libesp_common.a esp-idf/esp_timer/libesp_timer.a esp-idf/esp_event/libesp_event.a esp-idf/nvs_flash/libnvs_flash.a esp-idf/esp_phy/libesp_phy.a esp-idf/vfs/libvfs.a esp-idf/lwip/liblwip.a esp-idf/esp_netif/libesp_netif.a esp-idf/wpa_supplicant/libwpa_supplicant.a esp-idf/esp_coex/libesp_coex.a esp-idf/esp_wifi/libesp_wifi.a esp-idf/http_parser/libhttp_parser.a esp-idf/esp-tls/libesp-tls.a esp-idf/esp_adc/libesp_adc.a esp-idf/esp_eth/libesp_eth.a esp-idf/esp_gdbstub/libesp_gdbstub.a esp-idf/tcp_transport/libtcp_transport.a esp-idf/esp_http_client/libesp_http_client.a esp-idf/esp_http_server/libesp_http_server.a esp-idf/esp_https_ota/libesp_https_ota.a esp-idf/mbedtls/mbedtls/library/libmbedtls.a esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a esp-idf/mbedtls/mbedtls/library/libmbedx509.a esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a esp-idf/riscv/libriscv.a esp-idf/esp_ringbuf/libesp_ringbuf.a esp-idf/efuse/libefuse.a esp-idf/driver/libdriver.a esp-idf/esp_pm/libesp_pm.a esp-idf/mbedtls/libmbedtls.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/esp_partition/libesp_partition.a esp-idf/app_update/libapp_update.a esp-idf/esp_mm/libesp_mm.a esp-idf/spi_flash/libspi_flash.a esp-idf/pthread/libpthread.a esp-idf/esp_system/libesp_system.a esp-idf/esp_rom/libesp_rom.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/heap/libheap.a esp-idf/soc/libsoc.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/freertos/libfreertos.a esp-idf/newlib/libnewlib.a esp-idf/cxx/libcxx.a esp-idf/esp_common/libesp_common.a esp-idf/esp_timer/libesp_timer.a esp-idf/esp_event/libesp_event.a esp-idf/nvs_flash/libnvs_flash.a esp-idf/esp_phy/libesp_phy.a esp-idf/vfs/libvfs.a esp-idf/lwip/liblwip.a esp-idf/esp_netif/libesp_netif.a esp-idf/wpa_supplicant/libwpa_supplicant.a esp-idf/esp_coex/libesp_coex.a esp-idf/esp_wifi/libesp_wifi.a esp-idf/http_parser/libhttp_parser.a esp-idf/esp-tls/libesp-tls.a esp-idf/esp_adc/libesp_adc.a esp-idf/esp_eth/libesp_eth.a esp-idf/esp_gdbstub/libesp_gdbstub.a esp-idf/tcp_transport/libtcp_transport.a esp-idf/esp_http_client/libesp_http_client.a esp-idf/esp_http_server/libesp_http_server.a esp-idf/esp_https_ota/libesp_https_ota.a esp-idf/mbedtls/mbedtls/library/libmbedtls.a esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a esp-idf/mbedtls/mbedtls/library/libmbedx509.a esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a esp-idf/riscv/libriscv.a esp-idf/esp_ringbuf/libesp_ringbuf.a esp-idf/efuse/libefuse.a esp-idf/driver/libdriver.a esp-idf/esp_pm/libesp_pm.a esp-idf/mbedtls/libmbedtls.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/esp_partition/libesp_partition.a esp-idf/app_update/libapp_update.a esp-idf/esp_mm/libesp_mm.a esp-idf/spi_flash/libspi_flash.a esp-idf/pthread/libpthread.a esp-idf/esp_system/libesp_system.a esp-idf/esp_rom/libesp_rom.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/heap/libheap.a esp-idf/soc/libsoc.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/freertos/libfreertos.a esp-idf/newlib/libnewlib.a esp-idf/cxx/libcxx.a esp-idf/esp_common/libesp_common.a esp-idf/esp_timer/libesp_timer.a esp-idf/esp_event/libesp_event.a esp-idf/nvs_flash/libnvs_flash.a esp-idf/esp_phy/libesp_phy.a esp-idf/vfs/libvfs.a esp-idf/lwip/liblwip.a esp-idf/esp_netif/libesp_netif.a esp-idf/wpa_supplicant/libwpa_supplicant.a esp-idf/esp_coex/libesp_coex.a esp-idf/esp_wifi/libesp_wifi.a esp-idf/http_parser/libhttp_parser.a esp-idf/esp-tls/libesp-tls.a esp-idf/esp_adc/libesp_adc.a esp-idf/esp_eth/libesp_eth.a esp-idf/esp_gdbstub/libesp_gdbstub.a esp-idf/tcp_transport/libtcp_transport.a esp-idf/esp_http_client/libesp_http_client.a esp-idf/esp_http_server/libesp_http_server.a esp-idf/esp_https_ota/libesp_https_ota.a esp-idf/mbedtls/mbedtls/library/libmbedtls.a esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a esp-idf/mbedtls/mbedtls/library/libmbedx509.a esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a esp-idf/riscv/libriscv.a esp-idf/esp_ringbuf/libesp_ringbuf.a esp-idf/efuse/libefuse.a esp-idf/driver/libdriver.a esp-idf/esp_pm/libesp_pm.a esp-idf/mbedtls/libmbedtls.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/esp_partition/libesp_partition.a esp-idf/app_update/libapp_update.a esp-idf/esp_mm/libesp_mm.a esp-idf/spi_flash/libspi_flash.a esp-idf/pthread/libpthread.a esp-idf/esp_system/libesp_system.a esp-idf/esp_rom/libesp_rom.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/heap/libheap.a esp-idf/soc/libsoc.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/freertos/libfreertos.a esp-idf/newlib/libnewlib.a esp-idf/cxx/libcxx.a esp-idf/esp_common/libesp_common.a esp-idf/esp_timer/libesp_timer.a esp-idf/esp_event/libesp_event.a esp-idf/nvs_flash/libnvs_flash.a esp-idf/esp_phy/libesp_phy.a esp-idf/vfs/libvfs.a esp-idf/lwip/liblwip.a esp-idf/esp_netif/libesp_netif.a esp-idf/wpa_supplicant/libwpa_supplicant.a esp-idf/esp_coex/libesp_coex.a esp-idf/esp_wifi/libesp_wifi.a esp-idf/http_parser/libhttp_parser.a esp-idf/esp-tls/libesp-tls.a esp-idf/esp_adc/libesp_adc.a esp-idf/esp_eth/libesp_eth.a esp-idf/esp_gdbstub/libesp_gdbstub.a esp-idf/tcp_transport/libtcp_transport.a esp-idf/esp_http_client/libesp_http_client.a esp-idf/esp_http_server/libesp_http_server.a esp-idf/esp_https_ota/libesp_https_ota.a esp-idf/mbedtls/mbedtls/library/libmbedtls.a esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a esp-idf/mbedtls/mbedtls/library/libmbedx509.a esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a /home/<USER>/esp-idf-anviz/components/esp_coex/lib/esp32c6/libcoexist.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libcore.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libespnow.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libmesh.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libnet80211.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libpp.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libsmartconfig.a /home/<USER>/esp-idf-anviz/components/esp_wifi/lib/esp32c6/libwapi.a -u esp_app_desc -u pthread_include_pthread_impl -u pthread_include_pthread_cond_var_impl -u pthread_include_pthread_local_storage_impl -u pthread_include_pthread_rwlock_impl -u pthread_include_pthread_semaphore_impl -u start_app -u __ubsan_include -u __assert_func -u esp_crypto_dpa_prot_include_impl -Wl,--undefined=uxTopUsedPriority -Wl,--undefined=FreeRTOS_openocd_params -u app_main -lc -lm esp-idf/newlib/libnewlib.a -u newlib_include_heap_impl -u newlib_include_syscalls_impl -u newlib_include_pthread_impl -u newlib_include_assert_impl -Wl,--wrap=_Unwind_SetEnableExceptionFdeSorting -Wl,--wrap=__register_frame_info_bases -Wl,--wrap=__register_frame_info -Wl,--wrap=__register_frame -Wl,--wrap=__register_frame_info_table_bases -Wl,--wrap=__register_frame_info_table -Wl,--wrap=__register_frame_table -Wl,--wrap=__deregister_frame_info_bases -Wl,--wrap=__deregister_frame_info -Wl,--wrap=_Unwind_Find_FDE -Wl,--wrap=_Unwind_GetGR -Wl,--wrap=_Unwind_GetCFA -Wl,--wrap=_Unwind_GetIP -Wl,--wrap=_Unwind_GetIPInfo -Wl,--wrap=_Unwind_GetRegionStart -Wl,--wrap=_Unwind_GetDataRelBase -Wl,--wrap=_Unwind_GetTextRelBase -Wl,--wrap=_Unwind_SetIP -Wl,--wrap=_Unwind_SetGR -Wl,--wrap=_Unwind_GetLanguageSpecificData -Wl,--wrap=_Unwind_FindEnclosingFunction -Wl,--wrap=_Unwind_Resume -Wl,--wrap=_Unwind_RaiseException -Wl,--wrap=_Unwind_DeleteException -Wl,--wrap=_Unwind_ForcedUnwind -Wl,--wrap=_Unwind_Resume_or_Rethrow -Wl,--wrap=_Unwind_Backtrace -Wl,--wrap=__cxa_call_unexpected -Wl,--wrap=__gxx_personality_v0 -u __cxa_guard_dummy -lstdc++ esp-idf/pthread/libpthread.a -lgcc esp-idf/cxx/libcxx.a -u __cxx_fatal_exception -u include_esp_phy_override -lphy -lbtbb esp-idf/esp_phy/libesp_phy.a -lphy -lbtbb esp-idf/esp_phy/libesp_phy.a -lphy -lbtbb -u vfs_include_syscalls_impl 
