# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile C with /home/<USER>/.espressif/tools/riscv32-esp-elf/esp-12.2.0_20230208/riscv32-esp-elf/bin/riscv32-esp-elf-gcc
C_DEFINES = -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DUNITY_INCLUDE_CONFIG_H

C_INCLUDES = -I/home/<USER>/esp-idf-anviz/components/riscv/include -I/home/<USER>/test/coap/examples/coap_client/build/config -I/home/<USER>/esp-idf-anviz/components/newlib/platform_include -I/home/<USER>/esp-idf-anviz/components/freertos/FreeRTOS-Kernel/include -I/home/<USER>/esp-idf-anviz/components/freertos/FreeRTOS-Kernel/portable/riscv/include -I/home/<USER>/esp-idf-anviz/components/freertos/esp_additions/include/freertos -I/home/<USER>/esp-idf-anviz/components/freertos/esp_additions/include -I/home/<USER>/esp-idf-anviz/components/freertos/esp_additions/arch/riscv/include -I/home/<USER>/esp-idf-anviz/components/esp_hw_support/include -I/home/<USER>/esp-idf-anviz/components/esp_hw_support/include/soc -I/home/<USER>/esp-idf-anviz/components/esp_hw_support/include/soc/esp32c6 -I/home/<USER>/esp-idf-anviz/components/esp_hw_support/port/esp32c6/. -I/home/<USER>/esp-idf-anviz/components/esp_hw_support/port/esp32c6/private_include -I/home/<USER>/esp-idf-anviz/components/heap/include -I/home/<USER>/esp-idf-anviz/components/log/include -I/home/<USER>/esp-idf-anviz/components/soc/include -I/home/<USER>/esp-idf-anviz/components/soc/esp32c6 -I/home/<USER>/esp-idf-anviz/components/soc/esp32c6/include -I/home/<USER>/esp-idf-anviz/components/hal/esp32c6/include -I/home/<USER>/esp-idf-anviz/components/hal/include -I/home/<USER>/esp-idf-anviz/components/hal/platform_port/include -I/home/<USER>/esp-idf-anviz/components/esp_rom/include -I/home/<USER>/esp-idf-anviz/components/esp_rom/include/esp32c6 -I/home/<USER>/esp-idf-anviz/components/esp_rom/esp32c6 -I/home/<USER>/esp-idf-anviz/components/esp_common/include -I/home/<USER>/esp-idf-anviz/components/esp_system/include -I/home/<USER>/esp-idf-anviz/components/esp_system/port/soc -I/home/<USER>/esp-idf-anviz/components/esp_system/port/include/riscv -I/home/<USER>/esp-idf-anviz/components/esp_system/port/include/private -I/home/<USER>/esp-idf-anviz/components/lwip/include -I/home/<USER>/esp-idf-anviz/components/lwip/include/apps -I/home/<USER>/esp-idf-anviz/components/lwip/include/apps/sntp -I/home/<USER>/esp-idf-anviz/components/lwip/lwip/src/include -I/home/<USER>/esp-idf-anviz/components/lwip/port/include -I/home/<USER>/esp-idf-anviz/components/lwip/port/freertos/include -I/home/<USER>/esp-idf-anviz/components/lwip/port/esp32xx/include -I/home/<USER>/esp-idf-anviz/components/lwip/port/esp32xx/include/arch -I/home/<USER>/esp-idf-anviz/components/esp_ringbuf/include -I/home/<USER>/esp-idf-anviz/components/efuse/include -I/home/<USER>/esp-idf-anviz/components/efuse/esp32c6/include -I/home/<USER>/esp-idf-anviz/components/driver/include -I/home/<USER>/esp-idf-anviz/components/driver/deprecated -I/home/<USER>/esp-idf-anviz/components/driver/analog_comparator/include -I/home/<USER>/esp-idf-anviz/components/driver/dac/include -I/home/<USER>/esp-idf-anviz/components/driver/gpio/include -I/home/<USER>/esp-idf-anviz/components/driver/gptimer/include -I/home/<USER>/esp-idf-anviz/components/driver/i2c/include -I/home/<USER>/esp-idf-anviz/components/driver/i2s/include -I/home/<USER>/esp-idf-anviz/components/driver/ledc/include -I/home/<USER>/esp-idf-anviz/components/driver/mcpwm/include -I/home/<USER>/esp-idf-anviz/components/driver/parlio/include -I/home/<USER>/esp-idf-anviz/components/driver/pcnt/include -I/home/<USER>/esp-idf-anviz/components/driver/rmt/include -I/home/<USER>/esp-idf-anviz/components/driver/sdio_slave/include -I/home/<USER>/esp-idf-anviz/components/driver/sdmmc/include -I/home/<USER>/esp-idf-anviz/components/driver/sigma_delta/include -I/home/<USER>/esp-idf-anviz/components/driver/spi/include -I/home/<USER>/esp-idf-anviz/components/driver/temperature_sensor/include -I/home/<USER>/esp-idf-anviz/components/driver/touch_sensor/include -I/home/<USER>/esp-idf-anviz/components/driver/twai/include -I/home/<USER>/esp-idf-anviz/components/driver/uart/include -I/home/<USER>/esp-idf-anviz/components/driver/usb_serial_jtag/include -I/home/<USER>/esp-idf-anviz/components/esp_pm/include -I/home/<USER>/esp-idf-anviz/components/mbedtls/port/include -I/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/include -I/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/library -I/home/<USER>/esp-idf-anviz/components/mbedtls/esp_crt_bundle/include -I/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/3rdparty/everest/include -I/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/3rdparty/p256-m -I/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -I/home/<USER>/esp-idf-anviz/components/esp_app_format/include -I/home/<USER>/esp-idf-anviz/components/bootloader_support/include -I/home/<USER>/esp-idf-anviz/components/bootloader_support/bootloader_flash/include -I/home/<USER>/esp-idf-anviz/components/esp_partition/include -I/home/<USER>/esp-idf-anviz/components/app_update/include -I/home/<USER>/esp-idf-anviz/components/esp_mm/include -I/home/<USER>/esp-idf-anviz/components/spi_flash/include -I/home/<USER>/esp-idf-anviz/components/pthread/include -I/home/<USER>/esp-idf-anviz/components/esp_timer/include -I/home/<USER>/esp-idf-anviz/components/app_trace/include -I/home/<USER>/esp-idf-anviz/components/esp_event/include -I/home/<USER>/esp-idf-anviz/components/nvs_flash/include -I/home/<USER>/esp-idf-anviz/components/esp_phy/include -I/home/<USER>/esp-idf-anviz/components/esp_phy/esp32c6/include -I/home/<USER>/esp-idf-anviz/components/vfs/include -I/home/<USER>/esp-idf-anviz/components/esp_netif/include -I/home/<USER>/esp-idf-anviz/components/wpa_supplicant/include -I/home/<USER>/esp-idf-anviz/components/wpa_supplicant/port/include -I/home/<USER>/esp-idf-anviz/components/wpa_supplicant/esp_supplicant/include -I/home/<USER>/esp-idf-anviz/components/esp_coex/include -I/home/<USER>/esp-idf-anviz/components/esp_wifi/include -I/home/<USER>/esp-idf-anviz/components/esp_wifi/wifi_apps/include -I/home/<USER>/esp-idf-anviz/components/unity/include -I/home/<USER>/esp-idf-anviz/components/unity/unity/src -I/home/<USER>/esp-idf-anviz/components/cmock/CMock/src -I/home/<USER>/esp-idf-anviz/components/console -I/home/<USER>/esp-idf-anviz/components/http_parser -I/home/<USER>/esp-idf-anviz/components/esp-tls -I/home/<USER>/esp-idf-anviz/components/esp-tls/esp-tls-crypto -I/home/<USER>/esp-idf-anviz/components/esp_adc/include -I/home/<USER>/esp-idf-anviz/components/esp_adc/interface -I/home/<USER>/esp-idf-anviz/components/esp_adc/esp32c6/include -I/home/<USER>/esp-idf-anviz/components/esp_adc/deprecated/include -I/home/<USER>/esp-idf-anviz/components/esp_eth/include -I/home/<USER>/esp-idf-anviz/components/esp_gdbstub/include -I/home/<USER>/esp-idf-anviz/components/esp_hid/include -I/home/<USER>/esp-idf-anviz/components/tcp_transport/include -I/home/<USER>/esp-idf-anviz/components/esp_http_client/include -I/home/<USER>/esp-idf-anviz/components/esp_http_server/include -I/home/<USER>/esp-idf-anviz/components/esp_https_ota/include -I/home/<USER>/esp-idf-anviz/components/esp_psram/include -I/home/<USER>/esp-idf-anviz/components/esp_lcd/include -I/home/<USER>/esp-idf-anviz/components/esp_lcd/interface -I/home/<USER>/esp-idf-anviz/components/protobuf-c/protobuf-c -I/home/<USER>/esp-idf-anviz/components/protocomm/include/common -I/home/<USER>/esp-idf-anviz/components/protocomm/include/security -I/home/<USER>/esp-idf-anviz/components/protocomm/include/transports -I/home/<USER>/esp-idf-anviz/components/esp_local_ctrl/include -I/home/<USER>/esp-idf-anviz/components/espcoredump/include -I/home/<USER>/esp-idf-anviz/components/espcoredump/include/port/riscv -I/home/<USER>/esp-idf-anviz/components/wear_levelling/include -I/home/<USER>/esp-idf-anviz/components/sdmmc/include -I/home/<USER>/esp-idf-anviz/components/fatfs/diskio -I/home/<USER>/esp-idf-anviz/components/fatfs/vfs -I/home/<USER>/esp-idf-anviz/components/fatfs/src -I/home/<USER>/esp-idf-anviz/components/idf_test/include -I/home/<USER>/esp-idf-anviz/components/idf_test/include/esp32c6 -I/home/<USER>/esp-idf-anviz/components/ieee802154/include -I/home/<USER>/esp-idf-anviz/components/json/cJSON -I/home/<USER>/esp-idf-anviz/components/mqtt/esp-mqtt/include -I/home/<USER>/esp-idf-anviz/components/spiffs/include -I/home/<USER>/esp-idf-anviz/components/wifi_provisioning/include -I/home/<USER>/test/coap/port/include -I/home/<USER>/test/coap/libcoap/include -I/home/<USER>/test/coap/examples/coap_client/main -I/home/<USER>/esp-idf-anviz/examples/common_components/protocol_examples_common/include

C_FLAGS = -march=rv32imac_zicsr_zifencei 

