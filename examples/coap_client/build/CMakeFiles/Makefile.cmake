# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/esp-idf-anviz/.git/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bootloader/subproject/components/micro-ecc/micro-ecc/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/controller/lib_esp32/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/controller/lib_esp32c3_family/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/controller/lib_esp32c6/esp32c6-bt-lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/bt/host/nimble/nimble/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/cmock/CMock/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/esp_coex/lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/esp_phy/lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/heap/tlsf/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/ieee802154/lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/json/cJSON/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/lwip/lwip/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/mbedtls/mbedtls/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/mqtt/esp-mqtt/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/openthread/lib/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/openthread/openthread/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/protobuf-c/protobuf-c/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/spiffs/spiffs/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/modules/components/unity/unity/HEAD"
  "/home/<USER>/esp-idf-anviz/.git/refs/heads/dev"
  "/home/<USER>/esp-idf-anviz/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/app_trace/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/app_trace/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/app_update/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/bootloader/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/bootloader/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/bootloader/subproject/components/micro-ecc/micro-ecc/.git"
  "/home/<USER>/esp-idf-anviz/components/bootloader_support/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/bt/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/bt/controller/lib_esp32/.git"
  "/home/<USER>/esp-idf-anviz/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/.git"
  "/home/<USER>/esp-idf-anviz/components/bt/controller/lib_esp32c3_family/.git"
  "/home/<USER>/esp-idf-anviz/components/bt/controller/lib_esp32c6/esp32c6-bt-lib/.git"
  "/home/<USER>/esp-idf-anviz/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/.git"
  "/home/<USER>/esp-idf-anviz/components/bt/host/nimble/nimble/.git"
  "/home/<USER>/esp-idf-anviz/components/cmock/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/cmock/CMock/.git"
  "/home/<USER>/esp-idf-anviz/components/console/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/cxx/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/driver/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/efuse/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/efuse/esp32c6/sources.cmake"
  "/home/<USER>/esp-idf-anviz/components/esp-tls/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_adc/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_app_format/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_coex/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_coex/lib/.git"
  "/home/<USER>/esp-idf-anviz/components/esp_common/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_common/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/esp_eth/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_event/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_gdbstub/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_hid/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_http_client/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_http_server/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_https_ota/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_https_server/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_hw_support/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_hw_support/port/esp32c6/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_lcd/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_local_ctrl/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_mm/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_netif/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_netif_stack/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_partition/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_phy/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_phy/lib/.git"
  "/home/<USER>/esp-idf-anviz/components/esp_pm/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_psram/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_psram/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/esp_ringbuf/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_rom/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_system/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_system/ld/ld.cmake"
  "/home/<USER>/esp-idf-anviz/components/esp_system/port/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_system/port/soc/esp32c6/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_timer/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esp_wifi/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/espcoredump/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esptool_py/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/esptool_py/espefuse.cmake"
  "/home/<USER>/esp-idf-anviz/components/esptool_py/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/fatfs/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/fatfs/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/freertos/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/hal/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/heap/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/heap/tlsf/.git"
  "/home/<USER>/esp-idf-anviz/components/http_parser/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/idf_test/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/ieee802154/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/ieee802154/lib/.git"
  "/home/<USER>/esp-idf-anviz/components/json/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/json/cJSON/.git"
  "/home/<USER>/esp-idf-anviz/components/log/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/lwip/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/lwip/lwip/.git"
  "/home/<USER>/esp-idf-anviz/components/mbedtls/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/.git"
  "/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/3rdparty/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/3rdparty/everest/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/3rdparty/p256-m/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/include/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/mbedtls/mbedtls/library/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/mqtt/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/mqtt/esp-mqtt/.git"
  "/home/<USER>/esp-idf-anviz/components/mqtt/esp-mqtt/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/newlib/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/newlib/port/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/nvs_flash/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/openthread/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/openthread/lib/.git"
  "/home/<USER>/esp-idf-anviz/components/openthread/openthread/.git"
  "/home/<USER>/esp-idf-anviz/components/partition_table/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/partition_table/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/protobuf-c/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/protobuf-c/protobuf-c/.git"
  "/home/<USER>/esp-idf-anviz/components/protocomm/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/pthread/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/riscv/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/riscv/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/sdmmc/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/soc/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/spi_flash/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/spiffs/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/spiffs/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/spiffs/spiffs/.git"
  "/home/<USER>/esp-idf-anviz/components/tcp_transport/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/ulp/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/ulp/project_include.cmake"
  "/home/<USER>/esp-idf-anviz/components/unity/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/unity/unity/.git"
  "/home/<USER>/esp-idf-anviz/components/usb/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/vfs/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/wear_levelling/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/wifi_provisioning/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/components/wpa_supplicant/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/examples/common_components/protocol_examples_common/CMakeLists.txt"
  "/home/<USER>/esp-idf-anviz/tools/cmake/build.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/component.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/depgraph.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/dfu.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/git_submodules.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/idf.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/kconfig.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/ldgen.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/project.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/project_description.json.in"
  "/home/<USER>/esp-idf-anviz/tools/cmake/targets.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/third_party/GetGitRevisionDescription.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/third_party/GetGitRevisionDescription.cmake.in"
  "/home/<USER>/esp-idf-anviz/tools/cmake/tool_version_check.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/toolchain-esp32c6.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/uf2.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/utilities.cmake"
  "/home/<USER>/esp-idf-anviz/tools/cmake/version.cmake"
  "/home/<USER>/esp-idf-anviz/tools/kconfig_new/confgen.py"
  "/home/<USER>/esp-idf-anviz/tools/kconfig_new/config.env.in"
  "/home/<USER>/test/coap/CMakeLists.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.22.1/CMakeASMCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeSystem.cmake"
  "CMakeFiles/git-data/grabRef.cmake"
  "app-flash_args"
  "bootloader-flash_args"
  "bootloader-prefix/tmp/bootloader-cfgcmd.txt.in"
  "config/sdkconfig.cmake"
  "config/sdkconfig.h"
  "esp-idf/bootloader/bootloader-flash_args.in"
  "esp-idf/esptool_py/app-flash_args.in"
  "esp-idf/esptool_py/flasher_args.json.in"
  "esp-idf/partition_table/partition-table-flash_args.in"
  "flash_args"
  "flash_args.in"
  "ldgen_libraries.in"
  "../main/CMakeLists.txt"
  "../main/idf_component.yml"
  "../partitions.csv"
  "../sdkconfig"
  "/home/<USER>/test/coap/idf_component.yml"
  "/usr/share/cmake-3.22/Modules/CMakeASMInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakePackageConfigHelpers.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCXXCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/CheckIncludeFile.cmake"
  "/usr/share/cmake-3.22/Modules/CheckIncludeFileCXX.cmake"
  "/usr/share/cmake-3.22/Modules/CheckTypeSize.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-ASM.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.22/Modules/ExternalProject.cmake"
  "/usr/share/cmake-3.22/Modules/FindGit.cmake"
  "/usr/share/cmake-3.22/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.22/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.22/Modules/FindPython/Support.cmake"
  "/usr/share/cmake-3.22/Modules/FindPython3.cmake"
  "/usr/share/cmake-3.22/Modules/GNUInstallDirs.cmake"
  "/usr/share/cmake-3.22/Modules/Internal/CheckCompilerFlag.cmake"
  "/usr/share/cmake-3.22/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Generic.cmake"
  "/usr/share/cmake-3.22/Modules/WriteBasicConfigVersionFile.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/git-data/HEAD"
  "CMakeFiles/git-data/grabRef.cmake"
  "CMakeFiles/git-data/head-ref"
  "config.env"
  "bootloader-prefix/tmp/bootloader-cfgcmd.txt"
  "flash_args.in"
  "flash_args"
  "flash_project_args"
  "project_description.json"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/riscv/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_ringbuf/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/efuse/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/driver/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_pm/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/mbedtls/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/mbedtls/mbedtls/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/mbedtls/mbedtls/include/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/mbedtls/mbedtls/3rdparty/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/mbedtls/mbedtls/library/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_app_format/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/bootloader_support/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/bootloader/bootloader-flash_args.in"
  "bootloader-flash_args"
  "flash_bootloader_args"
  "esp-idf/bootloader/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esptool_py/app-flash_args.in"
  "app-flash_args"
  "flash_app_args"
  "esp-idf/esptool_py/flasher_args.json.in"
  "flasher_args.json"
  "esp-idf/esptool_py/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/partition_table/partition-table-flash_args.in"
  "partition-table-flash_args"
  "esp-idf/partition_table/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_partition/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/app_update/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_mm/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/spi_flash/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/pthread/CMakeFiles/CMakeDirectoryInformation.cmake"
  "ldgen_libraries.in"
  "ldgen_libraries"
  "esp-idf/esp_system/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_system/port/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_system/port/soc/esp32c6/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_rom/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/hal/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/log/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/heap/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/soc/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_hw_support/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_hw_support/port/esp32c6/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/freertos/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/newlib/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/newlib/port/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/cxx/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_common/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_timer/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/app_trace/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_event/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/nvs_flash/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_phy/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/vfs/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/lwip/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_netif_stack/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_netif/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/wpa_supplicant/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_coex/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_wifi/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/bt/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/unity/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/cmock/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/console/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/http_parser/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp-tls/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_adc/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_eth/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_gdbstub/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_hid/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/tcp_transport/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_http_client/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_http_server/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_https_ota/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_https_server/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_psram/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_lcd/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/protobuf-c/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/protocomm/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/esp_local_ctrl/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/espcoredump/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/wear_levelling/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/sdmmc/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/fatfs/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/idf_test/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/ieee802154/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/json/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/mqtt/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/openthread/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/spiffs/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/ulp/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/usb/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/wifi_provisioning/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/coap/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/main/CMakeFiles/CMakeDirectoryInformation.cmake"
  "esp-idf/protocol_examples_common/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/menuconfig.dir/DependInfo.cmake"
  "CMakeFiles/confserver.dir/DependInfo.cmake"
  "CMakeFiles/save-defconfig.dir/DependInfo.cmake"
  "CMakeFiles/bootloader.dir/DependInfo.cmake"
  "CMakeFiles/gen_project_binary.dir/DependInfo.cmake"
  "CMakeFiles/app.dir/DependInfo.cmake"
  "CMakeFiles/erase_flash.dir/DependInfo.cmake"
  "CMakeFiles/monitor.dir/DependInfo.cmake"
  "CMakeFiles/flash.dir/DependInfo.cmake"
  "CMakeFiles/encrypted-flash.dir/DependInfo.cmake"
  "CMakeFiles/_project_elf_src.dir/DependInfo.cmake"
  "CMakeFiles/coap_client.elf.dir/DependInfo.cmake"
  "CMakeFiles/size.dir/DependInfo.cmake"
  "CMakeFiles/size-files.dir/DependInfo.cmake"
  "CMakeFiles/size-components.dir/DependInfo.cmake"
  "CMakeFiles/uf2-app.dir/DependInfo.cmake"
  "CMakeFiles/uf2.dir/DependInfo.cmake"
  "esp-idf/riscv/CMakeFiles/__idf_riscv.dir/DependInfo.cmake"
  "esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/__idf_efuse.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse-common-table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse_common_table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse-custom-table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse_custom_table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/show-efuse-table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/show_efuse_table.dir/DependInfo.cmake"
  "esp-idf/efuse/CMakeFiles/efuse_test_table.dir/DependInfo.cmake"
  "esp-idf/driver/CMakeFiles/__idf_driver.dir/DependInfo.cmake"
  "esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/DependInfo.cmake"
  "esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/DependInfo.cmake"
  "esp-idf/mbedtls/CMakeFiles/custom_bundle.dir/DependInfo.cmake"
  "esp-idf/mbedtls/mbedtls/CMakeFiles/apidoc.dir/DependInfo.cmake"
  "esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/DependInfo.cmake"
  "esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/DependInfo.cmake"
  "esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/DependInfo.cmake"
  "esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/DependInfo.cmake"
  "esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/DependInfo.cmake"
  "esp-idf/mbedtls/mbedtls/library/CMakeFiles/lib.dir/DependInfo.cmake"
  "esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/DependInfo.cmake"
  "esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/DependInfo.cmake"
  "esp-idf/bootloader/CMakeFiles/bootloader-flash.dir/DependInfo.cmake"
  "esp-idf/bootloader/CMakeFiles/encrypted-bootloader-flash.dir/DependInfo.cmake"
  "esp-idf/esptool_py/CMakeFiles/app-flash.dir/DependInfo.cmake"
  "esp-idf/esptool_py/CMakeFiles/encrypted-app-flash.dir/DependInfo.cmake"
  "esp-idf/esptool_py/CMakeFiles/app_check_size.dir/DependInfo.cmake"
  "esp-idf/partition_table/CMakeFiles/partition_table_bin.dir/DependInfo.cmake"
  "esp-idf/partition_table/CMakeFiles/partition-table.dir/DependInfo.cmake"
  "esp-idf/partition_table/CMakeFiles/partition_table.dir/DependInfo.cmake"
  "esp-idf/partition_table/CMakeFiles/partition-table-flash.dir/DependInfo.cmake"
  "esp-idf/partition_table/CMakeFiles/encrypted-partition-table-flash.dir/DependInfo.cmake"
  "esp-idf/partition_table/CMakeFiles/partition_table-flash.dir/DependInfo.cmake"
  "esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/DependInfo.cmake"
  "esp-idf/app_update/CMakeFiles/__idf_app_update.dir/DependInfo.cmake"
  "esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/DependInfo.cmake"
  "esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/DependInfo.cmake"
  "esp-idf/pthread/CMakeFiles/__idf_pthread.dir/DependInfo.cmake"
  "esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/DependInfo.cmake"
  "esp-idf/esp_system/CMakeFiles/__ldgen_output_sections.ld.dir/DependInfo.cmake"
  "esp-idf/esp_system/CMakeFiles/memory_ld.dir/DependInfo.cmake"
  "esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/DependInfo.cmake"
  "esp-idf/hal/CMakeFiles/__idf_hal.dir/DependInfo.cmake"
  "esp-idf/log/CMakeFiles/__idf_log.dir/DependInfo.cmake"
  "esp-idf/heap/CMakeFiles/__idf_heap.dir/DependInfo.cmake"
  "esp-idf/soc/CMakeFiles/__idf_soc.dir/DependInfo.cmake"
  "esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/DependInfo.cmake"
  "esp-idf/freertos/CMakeFiles/__idf_freertos.dir/DependInfo.cmake"
  "esp-idf/newlib/CMakeFiles/__idf_newlib.dir/DependInfo.cmake"
  "esp-idf/cxx/CMakeFiles/__idf_cxx.dir/DependInfo.cmake"
  "esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/DependInfo.cmake"
  "esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/DependInfo.cmake"
  "esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/DependInfo.cmake"
  "esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/DependInfo.cmake"
  "esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/DependInfo.cmake"
  "esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/DependInfo.cmake"
  "esp-idf/vfs/CMakeFiles/__idf_vfs.dir/DependInfo.cmake"
  "esp-idf/lwip/CMakeFiles/__idf_lwip.dir/DependInfo.cmake"
  "esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/DependInfo.cmake"
  "esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/DependInfo.cmake"
  "esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/DependInfo.cmake"
  "esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/DependInfo.cmake"
  "esp-idf/unity/CMakeFiles/__idf_unity.dir/DependInfo.cmake"
  "esp-idf/cmock/CMakeFiles/__idf_cmock.dir/DependInfo.cmake"
  "esp-idf/console/CMakeFiles/__idf_console.dir/DependInfo.cmake"
  "esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/DependInfo.cmake"
  "esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/DependInfo.cmake"
  "esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/DependInfo.cmake"
  "esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/DependInfo.cmake"
  "esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/DependInfo.cmake"
  "esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/DependInfo.cmake"
  "esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/DependInfo.cmake"
  "esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/DependInfo.cmake"
  "esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/DependInfo.cmake"
  "esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/DependInfo.cmake"
  "esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/DependInfo.cmake"
  "esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/DependInfo.cmake"
  "esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/DependInfo.cmake"
  "esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/DependInfo.cmake"
  "esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/DependInfo.cmake"
  "esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/DependInfo.cmake"
  "esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/DependInfo.cmake"
  "esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/DependInfo.cmake"
  "esp-idf/ieee802154/CMakeFiles/__idf_ieee802154.dir/DependInfo.cmake"
  "esp-idf/json/CMakeFiles/__idf_json.dir/DependInfo.cmake"
  "esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/DependInfo.cmake"
  "esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/DependInfo.cmake"
  "esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/DependInfo.cmake"
  "esp-idf/coap/CMakeFiles/__idf_coap.dir/DependInfo.cmake"
  "esp-idf/main/CMakeFiles/__idf_main.dir/DependInfo.cmake"
  "esp-idf/protocol_examples_common/CMakeFiles/__idf_protocol_examples_common.dir/DependInfo.cmake"
  )
