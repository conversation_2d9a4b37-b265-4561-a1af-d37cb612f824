{"sources": [{"file": "/home/<USER>/test/coap/examples/coap_client/build/CMakeFiles/bootloader"}, {"file": "/home/<USER>/test/coap/examples/coap_client/build/CMakeFiles/bootloader.rule"}, {"file": "/home/<USER>/test/coap/examples/coap_client/build/CMakeFiles/bootloader-complete.rule"}, {"file": "/home/<USER>/test/coap/examples/coap_client/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "/home/<USER>/test/coap/examples/coap_client/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "/home/<USER>/test/coap/examples/coap_client/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "/home/<USER>/test/coap/examples/coap_client/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "/home/<USER>/test/coap/examples/coap_client/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "/home/<USER>/test/coap/examples/coap_client/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "/home/<USER>/test/coap/examples/coap_client/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}